import 'package:build_mate/data/shared_preferences/preferences_provider.dart';
import 'package:build_mate/firebase_options.dart';
import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:build_mate/presentation/screens/splash_screen.dart';
import 'package:build_mate/utils/constants.dart';
import 'package:build_mate/utils/state_logger.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'package:build_mate/presentation/routes/route_config.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final sharedPreferencesProvider = Provider<SharedPreferences>(
  (ref) => throw UnimplementedError(),
);

final asyncSharedPreferencesProvider = Provider<SharedPreferencesAsync>(
  (ref) => throw UnimplementedError(),
);

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Supabase.initialize(url: Constants.APIURL, anonKey: Constants.ANON_KEY);

  final prefs = await SharedPreferences.getInstance();
  final SharedPreferencesAsync asyncPrefs = SharedPreferencesAsync();

  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  OneSignal.Debug.setLogLevel(OSLogLevel.verbose);
  OneSignal.Debug.setAlertLevel(OSLogLevel.none);
  OneSignal.consentRequired(false);
  OneSignal.Notifications.requestPermission(true);

  // NOTE: Replace with your own app ID from https://www.onesignal.com
  OneSignal.initialize("************************************");
  // AndroidGoogleMapsFlutter.useAndroidViewSurface = true;
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.immersiveSticky,
    overlays: [],
  ).then(
    (_) => runApp(
      ProviderScope(
        overrides: [
          sharedPreferencesProvider.overrideWithValue(prefs),
          asyncSharedPreferencesProvider.overrideWithValue(asyncPrefs),
        ],
        observers: [Logger()],
        child: const MainApp(),
      ),
    ),
  );
}

final supabase = Supabase.instance.client;

class MainApp extends ConsumerStatefulWidget {
  const MainApp({super.key});

  @override
  ConsumerState<MainApp> createState() => _MainAppState();
}

class _MainAppState extends ConsumerState<MainApp> {
  bool _isInitializing = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeApp();
    });
  }

  

  Future<void> _initializeApp() async {
   
    if (supabase.auth.currentUser != null) {
      if (kDebugMode) {
        print('USER_IS_SIGNEDIN: ${supabase.auth.currentUser?.id}');
      }
      ref
          .read(preferencesProvider)
          .setSupabaseId(id: supabase.auth.currentUser?.id ?? '');

      // Check if user already has a profile in clients table
      await _checkUserProfile();
    } else {
      // Navigate to role selection screen if no user is authenticated
      ref.read(goRouterProvider).goNamed(RouteConstants.ROLE_SELECTION_SCREEN);
    }

    // Set initializing to false after navigation
    setState(() {
      _isInitializing = false;
    });
  }

  Future<void> _checkUserProfile() async {
    try {
      final userId = supabase.auth.currentUser?.id;
      if (userId == null) return;

      // Store user ID in preferences
      ref
          .read(preferencesProvider)
          .setSupabaseId(id: userId);

      // If on web platform, check for hardware shop profile
      if (kIsWeb) {
        if (kDebugMode) {
          print('Web platform detected, checking for hardware shop profile');
        }
        
        // Check if user has a hardware shop profile
        final shopResponse = await supabase
            .from('hardware_shops')
            .select()
            .eq('supabase_id', userId)
            .maybeSingle();
            
        if (shopResponse != null) {
          // User has a hardware shop profile, navigate to dashboard
          if (kDebugMode) {
            print('Hardware shop profile found, navigating to dashboard');
          }
          ref.read(goRouterProvider).goNamed(RouteConstants.HARDWARE_SHOP_DASHBOARD);
        } else {
          // No hardware shop profile, navigate to setup screen
          if (kDebugMode) {
            print('No hardware shop profile found, navigating to setup screen');
          }
          ref.read(goRouterProvider).goNamed(RouteConstants.SETUP_COMPANY_SCREEN);
        }
        return;
      }

      // For mobile platforms, continue with the existing flow
      // First, check if user exists in clients table
      final clientResponse =
          await supabase
              .from('clients')
              .select()
              .eq('supabase_id', userId)
              .maybeSingle();

      // If found in clients table, navigate to client home
      if (clientResponse != null) {
        if (kDebugMode) {
          print('User profile found in clients table: $clientResponse');
        }
        ref.read(goRouterProvider).goNamed(RouteConstants.CLIENT_HOME_SCREEN);
        return;
      }

      // If not found in clients, check artisans table
      final artisanResponse =
          await supabase
              .from('artisans')
              .select()
              .eq('supabase_id', userId)
              .maybeSingle();

      // If found in artisans table, navigate to service provider home
      if (artisanResponse != null) {
        if (kDebugMode) {
          print('User profile found in artisans table: $artisanResponse');
        }
        ref
            .read(goRouterProvider)
            .goNamed(RouteConstants.SERVICE_PROVIDER_HOME_SCREEN);
        return;
      }

      // If not found in either table, check preferences for role selection
      final isClient = ref.read(preferencesProvider).getIsClient();

      if (isClient) {
        // Navigate to client profile creation
        if (kDebugMode) {
          print(
            'User not found in any table, navigating to client profile creation',
          );
        }
        ref
            .read(goRouterProvider)
            .goNamed(RouteConstants.USER_PROFILE_DETAILS_SCREEN);
      } else {
        // Navigate to artisan profile creation
        if (kDebugMode) {
          print(
            'User not found in any table, navigating to artisan profile creation',
          );
        }
        ref
            .read(goRouterProvider)
            .goNamed(RouteConstants.ARTISAN_PROFILE_DETAILS_SCREEN);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error checking user profile: $e');
      }

      // If there's an error and on web, still go to setup company screen
      if (kIsWeb) {
        ref.read(goRouterProvider).goNamed(RouteConstants.SETUP_COMPANY_SCREEN);
        return;
      }

      // If there's an error, check preferences for role selection
      final isClient = ref.read(preferencesProvider).getIsClient();

      if (isClient) {
        // Default to client profile creation
        ref
            .read(goRouterProvider)
            .goNamed(RouteConstants.USER_PROFILE_DETAILS_SCREEN);
      } else {
        // Default to artisan profile creation
        ref
            .read(goRouterProvider)
            .goNamed(RouteConstants.ARTISAN_PROFILE_DETAILS_SCREEN);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final router = ref.watch(goRouterProvider);

    // Show splash screen while initializing
    if (_isInitializing) {
      return const MaterialApp(
        debugShowCheckedModeBanner: false,
        home: SplashScreen(),
      );
    }

    // Show the main app after initialization
    return MaterialApp.router(
      title: 'Build Mate',
      debugShowCheckedModeBanner: false,
      routerConfig: router,
    );
  }
}

// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'client_jobs_flow_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ClientJobsFlowState {

// @Default([]) List<JobModel> jobs,
 List<JobDetailsResponse> get jobs; List<GetBidResponse> get bids; bool get isLoading; String? get error; int get selectedTabIndex; JobDetailsResponse? get selectedJob; int get clientId; int get openJobsCount; int get inProgressJobsCount; int get completedJobsCount;
/// Create a copy of ClientJobsFlowState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ClientJobsFlowStateCopyWith<ClientJobsFlowState> get copyWith => _$ClientJobsFlowStateCopyWithImpl<ClientJobsFlowState>(this as ClientJobsFlowState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ClientJobsFlowState&&const DeepCollectionEquality().equals(other.jobs, jobs)&&const DeepCollectionEquality().equals(other.bids, bids)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.error, error) || other.error == error)&&(identical(other.selectedTabIndex, selectedTabIndex) || other.selectedTabIndex == selectedTabIndex)&&(identical(other.selectedJob, selectedJob) || other.selectedJob == selectedJob)&&(identical(other.clientId, clientId) || other.clientId == clientId)&&(identical(other.openJobsCount, openJobsCount) || other.openJobsCount == openJobsCount)&&(identical(other.inProgressJobsCount, inProgressJobsCount) || other.inProgressJobsCount == inProgressJobsCount)&&(identical(other.completedJobsCount, completedJobsCount) || other.completedJobsCount == completedJobsCount));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(jobs),const DeepCollectionEquality().hash(bids),isLoading,error,selectedTabIndex,selectedJob,clientId,openJobsCount,inProgressJobsCount,completedJobsCount);

@override
String toString() {
  return 'ClientJobsFlowState(jobs: $jobs, bids: $bids, isLoading: $isLoading, error: $error, selectedTabIndex: $selectedTabIndex, selectedJob: $selectedJob, clientId: $clientId, openJobsCount: $openJobsCount, inProgressJobsCount: $inProgressJobsCount, completedJobsCount: $completedJobsCount)';
}


}

/// @nodoc
abstract mixin class $ClientJobsFlowStateCopyWith<$Res>  {
  factory $ClientJobsFlowStateCopyWith(ClientJobsFlowState value, $Res Function(ClientJobsFlowState) _then) = _$ClientJobsFlowStateCopyWithImpl;
@useResult
$Res call({
 List<JobDetailsResponse> jobs, List<GetBidResponse> bids, bool isLoading, String? error, int selectedTabIndex, JobDetailsResponse? selectedJob, int clientId, int openJobsCount, int inProgressJobsCount, int completedJobsCount
});


$JobDetailsResponseCopyWith<$Res>? get selectedJob;

}
/// @nodoc
class _$ClientJobsFlowStateCopyWithImpl<$Res>
    implements $ClientJobsFlowStateCopyWith<$Res> {
  _$ClientJobsFlowStateCopyWithImpl(this._self, this._then);

  final ClientJobsFlowState _self;
  final $Res Function(ClientJobsFlowState) _then;

/// Create a copy of ClientJobsFlowState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? jobs = null,Object? bids = null,Object? isLoading = null,Object? error = freezed,Object? selectedTabIndex = null,Object? selectedJob = freezed,Object? clientId = null,Object? openJobsCount = null,Object? inProgressJobsCount = null,Object? completedJobsCount = null,}) {
  return _then(_self.copyWith(
jobs: null == jobs ? _self.jobs : jobs // ignore: cast_nullable_to_non_nullable
as List<JobDetailsResponse>,bids: null == bids ? _self.bids : bids // ignore: cast_nullable_to_non_nullable
as List<GetBidResponse>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,selectedTabIndex: null == selectedTabIndex ? _self.selectedTabIndex : selectedTabIndex // ignore: cast_nullable_to_non_nullable
as int,selectedJob: freezed == selectedJob ? _self.selectedJob : selectedJob // ignore: cast_nullable_to_non_nullable
as JobDetailsResponse?,clientId: null == clientId ? _self.clientId : clientId // ignore: cast_nullable_to_non_nullable
as int,openJobsCount: null == openJobsCount ? _self.openJobsCount : openJobsCount // ignore: cast_nullable_to_non_nullable
as int,inProgressJobsCount: null == inProgressJobsCount ? _self.inProgressJobsCount : inProgressJobsCount // ignore: cast_nullable_to_non_nullable
as int,completedJobsCount: null == completedJobsCount ? _self.completedJobsCount : completedJobsCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}
/// Create a copy of ClientJobsFlowState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$JobDetailsResponseCopyWith<$Res>? get selectedJob {
    if (_self.selectedJob == null) {
    return null;
  }

  return $JobDetailsResponseCopyWith<$Res>(_self.selectedJob!, (value) {
    return _then(_self.copyWith(selectedJob: value));
  });
}
}


/// @nodoc


class _ClientJobsFlowState implements ClientJobsFlowState {
   _ClientJobsFlowState({final  List<JobDetailsResponse> jobs = const [], final  List<GetBidResponse> bids = const [], this.isLoading = false, this.error, this.selectedTabIndex = 0, this.selectedJob = null, this.clientId = 0, this.openJobsCount = 0, this.inProgressJobsCount = 0, this.completedJobsCount = 0}): _jobs = jobs,_bids = bids;
  

// @Default([]) List<JobModel> jobs,
 final  List<JobDetailsResponse> _jobs;
// @Default([]) List<JobModel> jobs,
@override@JsonKey() List<JobDetailsResponse> get jobs {
  if (_jobs is EqualUnmodifiableListView) return _jobs;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_jobs);
}

 final  List<GetBidResponse> _bids;
@override@JsonKey() List<GetBidResponse> get bids {
  if (_bids is EqualUnmodifiableListView) return _bids;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_bids);
}

@override@JsonKey() final  bool isLoading;
@override final  String? error;
@override@JsonKey() final  int selectedTabIndex;
@override@JsonKey() final  JobDetailsResponse? selectedJob;
@override@JsonKey() final  int clientId;
@override@JsonKey() final  int openJobsCount;
@override@JsonKey() final  int inProgressJobsCount;
@override@JsonKey() final  int completedJobsCount;

/// Create a copy of ClientJobsFlowState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ClientJobsFlowStateCopyWith<_ClientJobsFlowState> get copyWith => __$ClientJobsFlowStateCopyWithImpl<_ClientJobsFlowState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ClientJobsFlowState&&const DeepCollectionEquality().equals(other._jobs, _jobs)&&const DeepCollectionEquality().equals(other._bids, _bids)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.error, error) || other.error == error)&&(identical(other.selectedTabIndex, selectedTabIndex) || other.selectedTabIndex == selectedTabIndex)&&(identical(other.selectedJob, selectedJob) || other.selectedJob == selectedJob)&&(identical(other.clientId, clientId) || other.clientId == clientId)&&(identical(other.openJobsCount, openJobsCount) || other.openJobsCount == openJobsCount)&&(identical(other.inProgressJobsCount, inProgressJobsCount) || other.inProgressJobsCount == inProgressJobsCount)&&(identical(other.completedJobsCount, completedJobsCount) || other.completedJobsCount == completedJobsCount));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_jobs),const DeepCollectionEquality().hash(_bids),isLoading,error,selectedTabIndex,selectedJob,clientId,openJobsCount,inProgressJobsCount,completedJobsCount);

@override
String toString() {
  return 'ClientJobsFlowState(jobs: $jobs, bids: $bids, isLoading: $isLoading, error: $error, selectedTabIndex: $selectedTabIndex, selectedJob: $selectedJob, clientId: $clientId, openJobsCount: $openJobsCount, inProgressJobsCount: $inProgressJobsCount, completedJobsCount: $completedJobsCount)';
}


}

/// @nodoc
abstract mixin class _$ClientJobsFlowStateCopyWith<$Res> implements $ClientJobsFlowStateCopyWith<$Res> {
  factory _$ClientJobsFlowStateCopyWith(_ClientJobsFlowState value, $Res Function(_ClientJobsFlowState) _then) = __$ClientJobsFlowStateCopyWithImpl;
@override @useResult
$Res call({
 List<JobDetailsResponse> jobs, List<GetBidResponse> bids, bool isLoading, String? error, int selectedTabIndex, JobDetailsResponse? selectedJob, int clientId, int openJobsCount, int inProgressJobsCount, int completedJobsCount
});


@override $JobDetailsResponseCopyWith<$Res>? get selectedJob;

}
/// @nodoc
class __$ClientJobsFlowStateCopyWithImpl<$Res>
    implements _$ClientJobsFlowStateCopyWith<$Res> {
  __$ClientJobsFlowStateCopyWithImpl(this._self, this._then);

  final _ClientJobsFlowState _self;
  final $Res Function(_ClientJobsFlowState) _then;

/// Create a copy of ClientJobsFlowState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? jobs = null,Object? bids = null,Object? isLoading = null,Object? error = freezed,Object? selectedTabIndex = null,Object? selectedJob = freezed,Object? clientId = null,Object? openJobsCount = null,Object? inProgressJobsCount = null,Object? completedJobsCount = null,}) {
  return _then(_ClientJobsFlowState(
jobs: null == jobs ? _self._jobs : jobs // ignore: cast_nullable_to_non_nullable
as List<JobDetailsResponse>,bids: null == bids ? _self._bids : bids // ignore: cast_nullable_to_non_nullable
as List<GetBidResponse>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,selectedTabIndex: null == selectedTabIndex ? _self.selectedTabIndex : selectedTabIndex // ignore: cast_nullable_to_non_nullable
as int,selectedJob: freezed == selectedJob ? _self.selectedJob : selectedJob // ignore: cast_nullable_to_non_nullable
as JobDetailsResponse?,clientId: null == clientId ? _self.clientId : clientId // ignore: cast_nullable_to_non_nullable
as int,openJobsCount: null == openJobsCount ? _self.openJobsCount : openJobsCount // ignore: cast_nullable_to_non_nullable
as int,inProgressJobsCount: null == inProgressJobsCount ? _self.inProgressJobsCount : inProgressJobsCount // ignore: cast_nullable_to_non_nullable
as int,completedJobsCount: null == completedJobsCount ? _self.completedJobsCount : completedJobsCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

/// Create a copy of ClientJobsFlowState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$JobDetailsResponseCopyWith<$Res>? get selectedJob {
    if (_self.selectedJob == null) {
    return null;
  }

  return $JobDetailsResponseCopyWith<$Res>(_self.selectedJob!, (value) {
    return _then(_self.copyWith(selectedJob: value));
  });
}
}

// dart format on

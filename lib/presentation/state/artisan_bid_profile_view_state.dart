import 'package:build_mate/data/dto/job_details_response.dart';
import 'package:build_mate/data/dto/responses_dto/artisan_profile_data_response.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'artisan_bid_profile_view_state.freezed.dart';

@freezed
abstract class ArtisanBidProfileViewState with _$ArtisanBidProfileViewState {
  factory ArtisanBidProfileViewState({
    @Default('') String name,
    @Default('') String profession,
    @Default('') String location,
    @Default(0.0) double distance,
    @Default(0.0) double rating,
    @Default(0) int totalReviews,
    @Default([]) List<String> certifications,
    @Default('') String serviceType,
    @Default(true) bool isActive,
    @Default([]) List<String> specializations,
    @Default('') String about,
    @Default('') String nextAvailableTime,
    @Default('assets/images/profile_pic.png') String coverImageUrl,
    @Default('assets/images/profile_pic.png') String profileImageUrl,
    @Default([]) List<String> workImages,
    @Default([]) List<Map<String, dynamic>> reviews,
    @Default(0) int selectedTabIndex,
    ArtisanProfileDataResponse? artisanProfileDataResponse,
    @Default(true) bool isLoading,
    JobDetailsResponse? selectedJob,
    @Default([]) List<Map<String, dynamic>> categories,
    @Default([]) List<String> availableSubcategories,
  }) = _ArtisanBidProfileViewState;
}



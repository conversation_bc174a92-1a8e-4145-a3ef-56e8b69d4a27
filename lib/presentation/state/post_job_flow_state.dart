import 'dart:io';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'post_job_flow_state.freezed.dart';

@freezed
abstract class PostJobFlowState with _$PostJobFlowState {
  factory PostJobFlowState({
    @Default('') String selectedMainCategory,
    @Default('') String selectedSubCategory,
    @Default([]) List<String> selectedSubCategories,
    @Default([]) List<String> relatedSubCategories,
    @Default(0) int selectedMainCategoryId,
    @Default('') String description,
    @Default('') String budget,
    @Default([]) List<File> jobImageFiles,
    DateTime? serviceDate,
    @Default(false) bool isLoading,
    @Default(false) bool isSubmitting,
    @Default({}) Map<String, int> subcategoryIdMap,
    @Default(0) int selectedServiceId,
  }) = _PostJobFlowState;
}

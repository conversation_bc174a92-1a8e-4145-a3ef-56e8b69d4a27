// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'job_posted_flow_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$JobPostedFlowState {

 dynamic get isLoading; dynamic get isCancellingLoading; List<PostedJobModel> get jobs; PostedJobModel? get selectedJob; int get clientId; int get selectedTabIndex;
/// Create a copy of JobPostedFlowState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$JobPostedFlowStateCopyWith<JobPostedFlowState> get copyWith => _$JobPostedFlowStateCopyWithImpl<JobPostedFlowState>(this as JobPostedFlowState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is JobPostedFlowState&&const DeepCollectionEquality().equals(other.isLoading, isLoading)&&const DeepCollectionEquality().equals(other.isCancellingLoading, isCancellingLoading)&&const DeepCollectionEquality().equals(other.jobs, jobs)&&(identical(other.selectedJob, selectedJob) || other.selectedJob == selectedJob)&&(identical(other.clientId, clientId) || other.clientId == clientId)&&(identical(other.selectedTabIndex, selectedTabIndex) || other.selectedTabIndex == selectedTabIndex));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(isLoading),const DeepCollectionEquality().hash(isCancellingLoading),const DeepCollectionEquality().hash(jobs),selectedJob,clientId,selectedTabIndex);

@override
String toString() {
  return 'JobPostedFlowState(isLoading: $isLoading, isCancellingLoading: $isCancellingLoading, jobs: $jobs, selectedJob: $selectedJob, clientId: $clientId, selectedTabIndex: $selectedTabIndex)';
}


}

/// @nodoc
abstract mixin class $JobPostedFlowStateCopyWith<$Res>  {
  factory $JobPostedFlowStateCopyWith(JobPostedFlowState value, $Res Function(JobPostedFlowState) _then) = _$JobPostedFlowStateCopyWithImpl;
@useResult
$Res call({
 dynamic isLoading, dynamic isCancellingLoading, List<PostedJobModel> jobs, PostedJobModel? selectedJob, int clientId, int selectedTabIndex
});




}
/// @nodoc
class _$JobPostedFlowStateCopyWithImpl<$Res>
    implements $JobPostedFlowStateCopyWith<$Res> {
  _$JobPostedFlowStateCopyWithImpl(this._self, this._then);

  final JobPostedFlowState _self;
  final $Res Function(JobPostedFlowState) _then;

/// Create a copy of JobPostedFlowState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isLoading = freezed,Object? isCancellingLoading = freezed,Object? jobs = null,Object? selectedJob = freezed,Object? clientId = null,Object? selectedTabIndex = null,}) {
  return _then(_self.copyWith(
isLoading: freezed == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as dynamic,isCancellingLoading: freezed == isCancellingLoading ? _self.isCancellingLoading : isCancellingLoading // ignore: cast_nullable_to_non_nullable
as dynamic,jobs: null == jobs ? _self.jobs : jobs // ignore: cast_nullable_to_non_nullable
as List<PostedJobModel>,selectedJob: freezed == selectedJob ? _self.selectedJob : selectedJob // ignore: cast_nullable_to_non_nullable
as PostedJobModel?,clientId: null == clientId ? _self.clientId : clientId // ignore: cast_nullable_to_non_nullable
as int,selectedTabIndex: null == selectedTabIndex ? _self.selectedTabIndex : selectedTabIndex // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// @nodoc


class _JobPostedFlowState implements JobPostedFlowState {
   _JobPostedFlowState({this.isLoading = false, this.isCancellingLoading = false, final  List<PostedJobModel> jobs = const [], this.selectedJob, this.clientId = 0, this.selectedTabIndex = 0}): _jobs = jobs;
  

@override@JsonKey() final  dynamic isLoading;
@override@JsonKey() final  dynamic isCancellingLoading;
 final  List<PostedJobModel> _jobs;
@override@JsonKey() List<PostedJobModel> get jobs {
  if (_jobs is EqualUnmodifiableListView) return _jobs;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_jobs);
}

@override final  PostedJobModel? selectedJob;
@override@JsonKey() final  int clientId;
@override@JsonKey() final  int selectedTabIndex;

/// Create a copy of JobPostedFlowState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$JobPostedFlowStateCopyWith<_JobPostedFlowState> get copyWith => __$JobPostedFlowStateCopyWithImpl<_JobPostedFlowState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _JobPostedFlowState&&const DeepCollectionEquality().equals(other.isLoading, isLoading)&&const DeepCollectionEquality().equals(other.isCancellingLoading, isCancellingLoading)&&const DeepCollectionEquality().equals(other._jobs, _jobs)&&(identical(other.selectedJob, selectedJob) || other.selectedJob == selectedJob)&&(identical(other.clientId, clientId) || other.clientId == clientId)&&(identical(other.selectedTabIndex, selectedTabIndex) || other.selectedTabIndex == selectedTabIndex));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(isLoading),const DeepCollectionEquality().hash(isCancellingLoading),const DeepCollectionEquality().hash(_jobs),selectedJob,clientId,selectedTabIndex);

@override
String toString() {
  return 'JobPostedFlowState(isLoading: $isLoading, isCancellingLoading: $isCancellingLoading, jobs: $jobs, selectedJob: $selectedJob, clientId: $clientId, selectedTabIndex: $selectedTabIndex)';
}


}

/// @nodoc
abstract mixin class _$JobPostedFlowStateCopyWith<$Res> implements $JobPostedFlowStateCopyWith<$Res> {
  factory _$JobPostedFlowStateCopyWith(_JobPostedFlowState value, $Res Function(_JobPostedFlowState) _then) = __$JobPostedFlowStateCopyWithImpl;
@override @useResult
$Res call({
 dynamic isLoading, dynamic isCancellingLoading, List<PostedJobModel> jobs, PostedJobModel? selectedJob, int clientId, int selectedTabIndex
});




}
/// @nodoc
class __$JobPostedFlowStateCopyWithImpl<$Res>
    implements _$JobPostedFlowStateCopyWith<$Res> {
  __$JobPostedFlowStateCopyWithImpl(this._self, this._then);

  final _JobPostedFlowState _self;
  final $Res Function(_JobPostedFlowState) _then;

/// Create a copy of JobPostedFlowState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isLoading = freezed,Object? isCancellingLoading = freezed,Object? jobs = null,Object? selectedJob = freezed,Object? clientId = null,Object? selectedTabIndex = null,}) {
  return _then(_JobPostedFlowState(
isLoading: freezed == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as dynamic,isCancellingLoading: freezed == isCancellingLoading ? _self.isCancellingLoading : isCancellingLoading // ignore: cast_nullable_to_non_nullable
as dynamic,jobs: null == jobs ? _self._jobs : jobs // ignore: cast_nullable_to_non_nullable
as List<PostedJobModel>,selectedJob: freezed == selectedJob ? _self.selectedJob : selectedJob // ignore: cast_nullable_to_non_nullable
as PostedJobModel?,clientId: null == clientId ? _self.clientId : clientId // ignore: cast_nullable_to_non_nullable
as int,selectedTabIndex: null == selectedTabIndex ? _self.selectedTabIndex : selectedTabIndex // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on

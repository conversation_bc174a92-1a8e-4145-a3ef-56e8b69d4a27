import 'package:build_mate/data/models/posted_job_model.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'job_posted_flow_state.freezed.dart';

@freezed
abstract class JobPostedFlowState with _$JobPostedFlowState {
  factory JobPostedFlowState({
    @Default(false) isLoading,
    @Default(false) isCancellingLoading,
    @Default([]) List<PostedJobModel> jobs,
    PostedJobModel? selectedJob,
    @Default(0) int clientId,
    @Default(0) int selectedTabIndex,
  }) = _JobPostedFlowState;
}

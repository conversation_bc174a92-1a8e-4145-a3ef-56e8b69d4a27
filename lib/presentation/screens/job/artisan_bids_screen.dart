import 'package:build_mate/presentation/view_models/job/client_jobs_view_model.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ArtisanBidsScreen extends ConsumerStatefulWidget {
  const ArtisanBidsScreen({super.key});

  @override
  ConsumerState<ArtisanBidsScreen> createState() => _ArtisanBidsScreenState();
}

class _ArtisanBidsScreenState extends ConsumerState<ArtisanBidsScreen> {
  @override
  void initState() {
    super.initState();
    // You can add initialization logic here if needed
    // For example, fetch bids for the selected job
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final jobsViewModel = ref.read(clientJobsViewModelProvider.notifier);
      final state = ref.read(clientJobsViewModelProvider);
      if (state.selectedJob != null) {
        jobsViewModel.fetchBidsForJob();
      }
    });
  }

  void _showRejectConfirmationDialog(BuildContext context, int bidId) {
    // Store a local reference to the context
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    bool isRejecting = false; // Track rejection state

    showDialog(
      context: context,
      builder:
          (dialogContext) => StatefulBuilder(
            // Use StatefulBuilder to update dialog state
            builder:
                (context, setDialogState) => AlertDialog(
                  title: Text(
                    'Reject Bid',
                    style: MyTypography.SemiBold.copyWith(fontSize: 18),
                  ),
                  content: Text(
                    'Are you sure you want to reject this artisan\'s bid? This action cannot be undone.',
                    style: MyTypography.Regular.copyWith(
                      color: Colors.black87,
                      fontSize: 14,
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed:
                          isRejecting
                              ? null
                              : () => Navigator.pop(dialogContext),
                      child: Text(
                        'Cancel',
                        style: MyTypography.Medium.copyWith(
                          color:
                              isRejecting ? Colors.grey[400] : Colors.grey[700],
                        ),
                      ),
                    ),
                    ElevatedButton(
                      onPressed:
                          isRejecting
                              ? null
                              : () async {
                                // Update dialog state to show loading
                                setDialogState(() {
                                  isRejecting = true;
                                });

                                final jobsViewModel = ref.read(
                                  clientJobsViewModelProvider.notifier,
                                );

                                try {
                                  // Reject the bid
                                  final response = await jobsViewModel
                                      .rejectBid(bidId: bidId);

                                  if (context.mounted) {
                                    // Close dialog first
                                    context.pop();
                                  }

                                  if (response.success == true) {
                                    scaffoldMessenger.showSnackBar(
                                      const SnackBar(
                                        content: Text(
                                          'Bid rejected successfully',
                                        ),
                                        backgroundColor: Colors.green,
                                      ),
                                    );
                                    // Refresh the bids list
                                    jobsViewModel.fetchBidsForJob();
                                  } else {
                                    scaffoldMessenger.showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          'Failed to reject bid: ${response.errorMessage}',
                                        ),
                                        backgroundColor: Colors.red,
                                      ),
                                    );
                                  }
                                } catch (e) {
                                  // Close dialog if error occurs
                                  if (context.mounted) {
                                    context.pop();
                                  }

                                  scaffoldMessenger.showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        'Error rejecting bid: ${e.toString()}',
                                      ),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                }
                              },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        disabledBackgroundColor: Colors.red.withAlpha(
                          (0.5 * 255).round(),
                        ),
                        disabledForegroundColor: Colors.white70,
                      ),
                      child:
                          isRejecting
                              ? Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  const Text('Rejecting...'),
                                ],
                              )
                              : const Text('Reject'),
                    ),
                  ],
                ),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final jobsViewModel = ref.read(clientJobsViewModelProvider.notifier);
    final state = ref.watch(clientJobsViewModelProvider);

    // Add a map to track loading state for each bid
    final Map<int, bool> acceptingBids = {};

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Artisan Bids',
          style: MyTypography.SemiBold.copyWith(fontSize: 18),
        ),
        backgroundColor: darkBlueColor,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Job summary card
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      state.selectedJob?.service?.name ?? 'Job Title',
                      style: MyTypography.SemiBold.copyWith(fontSize: 18),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      state.selectedJob?.jobDescription ?? 'No description',
                      style: MyTypography.Regular.copyWith(
                        fontSize: 14,
                        color: Colors.grey[700],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Bids: ${state.selectedJob?.bids?.length ?? 0}',
                      style: MyTypography.Medium.copyWith(
                        fontSize: 14,
                        color: orangeColor,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Artisan bids list header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              'Artisans who responded',
              style: MyTypography.SemiBold.copyWith(fontSize: 16),
            ),
          ),
          const SizedBox(height: 8),
          // Artisan bids list
          Expanded(
            child:
                state.selectedJob?.bids == null ||
                        state.selectedJob!.bids!.isEmpty
                    ? Center(
                      child: Text(
                        'No bids yet',
                        style: MyTypography.Medium.copyWith(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                    )
                    : StatefulBuilder(
                      builder: (context, setState) {
                        return ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: state.bids.length,
                          itemBuilder: (context, index) {
                            final bid = state.bids[index];
                            final bidId = bid.id ?? 0;
                            final isAccepting = acceptingBids[bidId] ?? false;

                            return Padding(
                              padding: const EdgeInsets.only(bottom: 16),
                              child: Card(
                                elevation: 2,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // Artisan info row
                                      Row(
                                        children: [
                                          // Artisan profile picture
                                          CircleAvatar(
                                            radius: 24,
                                            backgroundColor: Colors.grey[200],
                                            backgroundImage:
                                                bid.artisan?.avatarUrl! !=
                                                            null &&
                                                        bid
                                                            .artisan!
                                                            .avatarUrl!
                                                            .isNotEmpty
                                                    ? bid.artisan!.avatarUrl!
                                                            .startsWith('http')
                                                        ? NetworkImage(
                                                              bid
                                                                      .artisan
                                                                      ?.avatarUrl ??
                                                                  '',
                                                            )
                                                            as ImageProvider
                                                        : AssetImage(
                                                          bid
                                                                  .artisan
                                                                  ?.avatarUrl ??
                                                              '',
                                                        )
                                                    : null,
                                            child:
                                                bid.artisan?.avatarUrl ==
                                                            null ||
                                                        bid
                                                            .artisan!
                                                            .avatarUrl!
                                                            .isEmpty
                                                    ? Icon(
                                                      Icons.person,
                                                      size: 24,
                                                      color: Colors.grey[400],
                                                    )
                                                    : null,
                                          ),
                                          const SizedBox(width: 12),

                                          // Artisan name and rating
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  bid.artisan?.name ??
                                                      'Unknown Artisan',
                                                  style: MyTypography
                                                      .SemiBold.copyWith(
                                                    fontSize: 16,
                                                  ),
                                                ),
                                                const SizedBox(height: 4),
                                                Row(
                                                  children: [
                                                    Icon(
                                                      Icons.star,
                                                      size: 16,
                                                      color: Colors.amber,
                                                    ),
                                                    const SizedBox(width: 4),
                                                    Text(
                                                      '5',
                                                      style: MyTypography
                                                          .Medium.copyWith(
                                                        fontSize: 14,
                                                        color: Colors.grey[700],
                                                      ),
                                                    ),
                                                    const SizedBox(width: 4),
                                                    Text(
                                                      '(2 reviews)',
                                                      style: MyTypography
                                                          .Regular.copyWith(
                                                        fontSize: 12,
                                                        color: Colors.grey[600],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),

                                      const SizedBox(height: 12),

                                      // Artisan description
                                      Text(
                                        bid.artisan?.about ??
                                            'No description available',
                                        style: MyTypography.Regular.copyWith(
                                          fontSize: 14,
                                          color: Colors.grey[700],
                                        ),
                                        maxLines: 3,
                                        overflow: TextOverflow.ellipsis,
                                      ),

                                      const SizedBox(height: 16),

                                      // Accept/Reject buttons
                                      Row(
                                        children: [
                                          Expanded(
                                            child: OutlinedButton(
                                              onPressed:
                                                  isAccepting
                                                      ? null
                                                      : () {
                                                        _showRejectConfirmationDialog(
                                                          context,
                                                          bid.id ?? 0,
                                                        );
                                                      },
                                              style: OutlinedButton.styleFrom(
                                                foregroundColor: Colors.red,
                                                side: const BorderSide(
                                                  color: Colors.red,
                                                ),
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                      vertical: 12,
                                                    ),
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                ),
                                              ),
                                              child: const Text('Reject'),
                                            ),
                                          ),
                                          const SizedBox(width: 12),
                                          Expanded(
                                            child: ElevatedButton(
                                              onPressed:
                                                  isAccepting
                                                      ? null
                                                      : () async {
                                                        // Update loading state
                                                        setState(() {
                                                          acceptingBids[bidId] =
                                                              true;
                                                        });

                                                        try {
                                                          // Accept bid logic
                                                          final response =
                                                              await jobsViewModel.acceptBid(
                                                                artisanId:
                                                                    bid
                                                                        .artisan
                                                                        ?.id ??
                                                                    0,
                                                                jobId:
                                                                    bid
                                                                        .job
                                                                        ?.id ??
                                                                    0,
                                                                bidId:
                                                                    bid.id ?? 0,
                                                              );

                                                          if (response['success']) {
                                                            if (context
                                                                .mounted) {
                                                              ScaffoldMessenger.of(
                                                                context,
                                                              ).showSnackBar(
                                                                const SnackBar(
                                                                  content: Text(
                                                                    'Bid accepted',
                                                                  ),
                                                                  backgroundColor:
                                                                      Colors
                                                                          .green,
                                                                ),
                                                              );
                                                              context.pop();
                                                            }
                                                          } else {
                                                            if (context
                                                                .mounted) {
                                                              ScaffoldMessenger.of(
                                                                context,
                                                              ).showSnackBar(
                                                                SnackBar(
                                                                  content: Text(
                                                                    'Failed to accept bid: ${response['error']}',
                                                                  ),
                                                                  backgroundColor:
                                                                      Colors
                                                                          .red,
                                                                ),
                                                              );
                                                            }

                                                            // Reset loading state on error
                                                            setState(() {
                                                              acceptingBids[bidId] =
                                                                  false;
                                                            });
                                                          }
                                                        } catch (e) {
                                                          if (context.mounted) {
                                                            ScaffoldMessenger.of(
                                                              context,
                                                            ).showSnackBar(
                                                              SnackBar(
                                                                content: Text(
                                                                  'Error accepting bid: ${e.toString()}',
                                                                ),
                                                                backgroundColor:
                                                                    Colors.red,
                                                              ),
                                                            );
                                                          }

                                                          // Reset loading state on error
                                                          setState(() {
                                                            acceptingBids[bidId] =
                                                                false;
                                                          });
                                                        }
                                                      },
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor: orangeColor,
                                                foregroundColor: Colors.white,
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                      vertical: 12,
                                                    ),
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                ),
                                                disabledBackgroundColor:
                                                    orangeColor.withAlpha(
                                                      (0.7 * 255).round(),
                                                    ),
                                              ),
                                              child:
                                                  isAccepting
                                                      ? Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .center,
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          SizedBox(
                                                            width: 16,
                                                            height: 16,
                                                            child: CircularProgressIndicator(
                                                              strokeWidth: 2,
                                                              valueColor:
                                                                  AlwaysStoppedAnimation<
                                                                    Color
                                                                  >(
                                                                    Colors
                                                                        .white,
                                                                  ),
                                                            ),
                                                          ),
                                                          const SizedBox(
                                                            width: 8,
                                                          ),
                                                          const Text(
                                                            'Accepting...',
                                                          ),
                                                        ],
                                                      )
                                                      : const Text('Accept'),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        );
                      },
                    ),
          ),
        ],
      ),
    );
  }
}

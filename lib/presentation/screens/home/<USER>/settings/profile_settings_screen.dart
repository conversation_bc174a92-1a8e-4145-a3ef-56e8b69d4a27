import 'package:build_mate/presentation/view_models/user/profile_settings_view_model.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ProfileSettingsScreen extends ConsumerStatefulWidget {
  const ProfileSettingsScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _ProfileSettingsScreenState();
}

class _ProfileSettingsScreenState extends ConsumerState<ProfileSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    final state = ref.watch(profileSettingsViewModelProvider);
    final notifier = ref.watch(profileSettingsViewModelProvider.notifier);

    final TextEditingController nameController = TextEditingController(
      text: state.username,
    );

    final GlobalKey _imageKey = GlobalKey();

    return Scaffold(
      appBar: AppBar(title: const Text('Profile Settings'), centerTitle: true),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Avatar with edit button
            Stack(
              alignment: Alignment.bottomRight,
              children: [
                CircleAvatar(
                  key: _imageKey,
                  radius: 54,
                  backgroundImage:
                      state.profileUrl != ''
                          ? NetworkImage(state.profileUrl)
                          : null,
                  backgroundColor: Colors.grey[200],
                  child:
                      state.profileUrl == ''
                          ? Icon(
                            Icons.person,
                            size: 54,
                            color: Colors.grey[400],
                          )
                          : null,
                ),
                Positioned(
                  bottom: 4,
                  right: 4,
                  child: InkWell(
                    onTap: () async {
                      await notifier.pickAndUploadAvatar().then((_){
                        _imageKey.currentState?.setState(() {});
                      });
                    },
                    borderRadius: BorderRadius.circular(24),
                    child: Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        shape: BoxShape.circle,
                      ),
                      padding: const EdgeInsets.all(8),
                      child: const Icon(
                        Icons.camera_alt,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 32),
            // Editable name field
            TextField(
              controller: nameController,
              decoration: InputDecoration(
                labelText: 'Customer Name',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.save),
                  onPressed: () {
                    if (nameController.text.trim().isNotEmpty &&
                        nameController.text.trim() != state.username) {
                      notifier.updateUserName(nameController.text.trim());
                      FocusScope.of(context).unfocus();
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Name updated')),
                      );
                    }
                  },
                ),
              ),
              textInputAction: TextInputAction.done,
              onSubmitted: (value) {
                if (value.trim().isNotEmpty && value.trim() != state.username) {
                  notifier.updateUserName(value.trim());
                  ScaffoldMessenger.of(
                    context,
                  ).showSnackBar(const SnackBar(content: Text('Name updated')));
                }
              },
            ),
            const SizedBox(height: 16),
            // ...additional settings widgets if needed...
            if (state.isLoading)
              const Padding(
                padding: EdgeInsets.only(top: 24),
                child: CircularProgressIndicator(),
              ),
            if (state.errorMessage != null)
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Text(
                  state.errorMessage!,
                  style: const TextStyle(color: Colors.red),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

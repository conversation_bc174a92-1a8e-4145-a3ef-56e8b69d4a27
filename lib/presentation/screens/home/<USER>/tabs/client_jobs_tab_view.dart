import 'package:build_mate/data/models/posted_job_model.dart';
import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:build_mate/presentation/view_models/artisan/job_posted_view_model.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/presentation/components/cards/job_listing_card.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shimmer/shimmer.dart';


class ClientJobsTabView extends ConsumerStatefulWidget {
  const ClientJobsTabView({super.key});

  @override
  ConsumerState<ClientJobsTabView> createState() => _ClientJobsTabViewState();
}

class _ClientJobsTabViewState extends ConsumerState<ClientJobsTabView>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true; // Keep the state alive when switching tabs

  // Add this key to track list changes
  final GlobalKey<AnimatedListState> _listKey = GlobalKey<AnimatedListState>();
  List<PostedJobModel> _previousJobs = [];
  

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Only refresh if the widget is mounted
    if (mounted) {
      // Use a microtask to avoid calling setState during build
      Future.microtask(() {
        final viewModel = ref.read(jobPostedViewModelProvider.notifier);
        viewModel.refreshJobsStream();
      });
    }
  }

  @override
  void initState() {
    super.initState();
    // Initialize with the appropriate tab based on intent
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Default to "New Jobs" tab (index 0)
      ref.read(jobPostedViewModelProvider.notifier).setSelectedTabIndex(0);
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    final viewModel = ref.watch(jobPostedViewModelProvider.notifier);
    // final state = ref.watch(jobPostedViewModelProvider);

    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          Container(
            color: darkBlueColor,
            child: SafeArea(
              bottom: false,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Available Jobs',
                      style: MyTypography.SemiBold.copyWith(
                        fontSize: 24,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Tab selector
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                _buildTab('New', 0, viewModel),
                _buildTab('My Jobs', 1, viewModel),
                _buildTab('Completed', 2, viewModel),
              ],
            ),
          ),

          // Job list with StreamBuilder
          Expanded(
            child: StreamBuilder<List<PostedJobModel>>(
              stream: viewModel.jobsStream,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting &&
                    !snapshot.hasData) {
                  return _buildSkeletonLoader();
                }

                if (snapshot.hasError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 48,
                          color: Colors.red[300],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading jobs: ${snapshot.error}',
                          style: MyTypography.Medium.copyWith(
                            color: Colors.red[700],
                          ),
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton(
                          onPressed:
                              () => viewModel.fetchAvailableJobsForArtisan(),
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }

                final jobs = snapshot.data ?? [];

                if (jobs.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.work_outline,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No jobs found',
                          style: MyTypography.Medium.copyWith(
                            fontSize: 16,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  );
                }

                // Find new jobs by comparing with previous list
                final Set<int> previousJobIds = 
                    _previousJobs.map((job) => job.id).toSet();
                final List<bool> isNewJob = jobs.map((job) => 
                    !previousJobIds.contains(job.id)).toList();
                
                // Update previous jobs list for next comparison
                _previousJobs = List.from(jobs);

                return RefreshIndicator(
                  onRefresh: () => viewModel.fetchAvailableJobsForArtisan(),
                  child: AnimationLimiter(
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: jobs.length,
                      itemBuilder: (context, index) {
                        final job = jobs[index];
                        return _buildJobWithHighlight(job, isNewJob[index], index);
                      },
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTab(String title, int index, JobPostedViewModel viewModel) {
    // Determine if this tab is selected based on the view model's selected tab index
    final isSelected = viewModel.selectedTabIndex == index;

    // Map index to job status
    // Tab 0 (New): corresponds to "open" status
    // Tab 1 (My Jobs): corresponds to "in_progress" status and my artisan ID
    // Tab 2 (Completed): corresponds to "completed" status

    return Expanded(
      child: GestureDetector(
        onTap: () {
          // When tab is tapped, update the selected tab index in the view model
          // This will trigger fetchJobsByTab() which will fetch the appropriate jobs based on status
          viewModel.setSelectedTabIndex(index);
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected ? darkBlueColor.withAlpha((0.85 * 255).round()) : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            boxShadow: isSelected ? [
              BoxShadow(
                color: darkBlueColor.withAlpha((0.1 * 255).round()),
                spreadRadius: 0.2,
                blurRadius: 2,
                offset: const Offset(0, 1),
              )
            ] : null,
          ),
          child: Center(
            child: Text(
              title,
              style: MyTypography.Medium.copyWith(
                fontSize: 14,
                color: isSelected ? Colors.white : Colors.black54,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSkeletonLoader() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 5, // Show 5 skeleton items
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Container(
            // Approximate height of a job card
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withAlpha((0.05 * 255).round()), // Reduced opacity from 0.1 to 0.05
                  spreadRadius: 0.5, // Reduced from 1 to 0.5
                  blurRadius: 3, // Reduced from 4 to 3
                  offset: const Offset(0, 1), // Reduced y-offset from 2 to 1
                ),
              ],
            ),
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Client avatar and name row
                    Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Container(
                          width: 120,
                          height: 16,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Title
                    Container(
                      width: double.infinity,
                      height: 20,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Tags
                    Row(
                      children: List.generate(
                        3,
                        (index) => Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: Container(
                            width: 60,
                            height: 24,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Description
                    Container(
                      width: double.infinity,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity * 0.7,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Bottom row with budget and button
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          width: 80,
                          height: 20,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        Container(
                          width: 100,
                          height: 36,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(18),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // Add this method to highlight new jobs
  Widget _buildJobWithHighlight(PostedJobModel job, bool isNew, int index) {
    return AnimationConfiguration.staggeredList(
      position: index,
      duration: const Duration(milliseconds: 375),
      child: SlideAnimation(
        verticalOffset: 50.0,
        child: FadeInAnimation(
          child: TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: isNew ? 0.0 : 1.0, end: 1.0),
            duration: Duration(milliseconds: isNew ? 1000 : 0),
            builder: (context, value, child) {
              return Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: isNew
                      ? [
                          BoxShadow(
                            color: darkBlueColor.withAlpha((0.15 * value).round()), // Reduced opacity from 0.3 to 0.15
                            spreadRadius: 1 * value, // Reduced from 2 to 1
                            blurRadius: 6 * value, // Reduced from 10 to 6
                          )
                        ]
                      : null,
                ),
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: JobListingCard(
                    clientName: job.clientName,
                    clientAvatarUrl:
                        job.clientAvatar ?? 'assets/images/avatar_placeholder.png',
                    mainCategory: job.title,
                    tags: job.categories,
                    serviceDate: job.serviceDate,
                    description: job.description,
                    postedAt: job.postDate,
                    budget: job.budget,
                    bidCount: job.bids.length,
                    onTap: () {
                      ref.read(jobPostedViewModelProvider.notifier).setSelectedJob(job);
                      context.pushNamed(
                        RouteConstants.JOB_OFFER_DETAILS_SCREEN,
                      );
                    },
                    isLoading: ref.read(jobPostedViewModelProvider.notifier).isJobLoading(job.id),
                    onBidTap: () async {
                      try {
                        await ref.read(jobPostedViewModelProvider.notifier).bid(job);
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                'You have expressed interest in this job!',
                              ),
                              backgroundColor: Colors.green,
                            ),
                          );
                        }
                      } catch (e) {
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'Failed to express interest: ${e.toString()}',
                              ),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      }
                    },
                    onCompleteJobTap: () {
                      // Implement job completion logic
                      // This will be called when the user clicks "Complete Job"
                      // You'll need to implement this method in the view model
                      // For now, just show a message
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Job completion feature coming soon'),
                          backgroundColor: Colors.blue,
                        ),
                      );
                    },
                    hasMyBid: job.hasMyBid,
                    isSelected: job.bids.any((bid) => bid['is_selected'] == true),
                    status: job.status,
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}

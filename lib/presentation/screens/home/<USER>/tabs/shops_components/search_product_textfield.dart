import 'package:build_mate/presentation/screens/home/<USER>/tabs/shops_components/subcategory_filter_sheet.dart';
import 'package:build_mate/presentation/state/shop_products_state.dart';
import 'package:build_mate/presentation/view_models/user/shop_products_view_model.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'dart:async';

class SearchProductTextField extends ConsumerStatefulWidget {
  const SearchProductTextField({super.key});
  @override
  ConsumerState<SearchProductTextField> createState() =>
      _SearchProductTextFieldState();
}

// Add this widget below your existing code

class _SearchProductTextFieldState
    extends ConsumerState<SearchProductTextField> {
  Timer? _debounce;

  void _onChanged(String value, void Function(String) onSearchProduct) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      onSearchProduct(value);
    });
  }

  Future<void> _showSubcategoriesSheet(
    BuildContext context,
    ShopProductsState state,
  ) async {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      isScrollControlled: true,
      builder: (_) {
        return FractionallySizedBox(
          widthFactor: 1,
          heightFactor: 0.9,
          child: SubcategoryFilterSheet(subcategories: state.subcategories),
        );
      },
    );
  }

  @override
  void dispose() {
    _debounce?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // final state = ref.watch(shopProductsViewModelProvider);
    final viewModel = ref.watch(shopProductsViewModelProvider.notifier);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(Icons.search, color: Colors.grey[600], size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Find your products',
                hintStyle: TextStyle(color: Colors.grey[600], fontSize: 16),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
              onChanged:
                  (value) => _onChanged(value, viewModel.onSearchProduct),
              style: const TextStyle(fontSize: 16),
            ),
          ),
          GestureDetector(
            onTap: () {
              _showSubcategoriesSheet(
                context,
                ref.watch(shopProductsViewModelProvider),
              );
            },
            child: Icon(Icons.tune, color: Colors.grey[600], size: 20),
          ),
        ],
      ),
    );
  }
}

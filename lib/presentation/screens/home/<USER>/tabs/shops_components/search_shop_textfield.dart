import 'package:build_mate/presentation/view_models/user/shops_view_model.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'dart:async';

class SearchShopTextField extends ConsumerStatefulWidget {
  const SearchShopTextField({super.key});
  @override
  ConsumerState<SearchShopTextField> createState() =>
      _SearchShopTextFieldState();
}

class _SearchShopTextFieldState extends ConsumerState<SearchShopTextField> {
  Timer? _debounceTimer;
  final TextEditingController _distanceController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();

  String _lastSearch = '';

  @override
  void initState() {
    super.initState();
    // Prevent controller text from being reset on rebuilds by not setting it from state
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _distanceController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onDistanceChanged(String value) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      final distance = double.tryParse(value);
      if (distance != null && distance > 0) {
        final viewModel = ref.read(shopsViewModelProvider.notifier);
        viewModel.setRadius(distance);
        final currentLocation = ref.read(shopsViewModelProvider).userLocation;
        if (currentLocation != null) {
          viewModel.getNearbyShops(
            latitude: currentLocation.latitude,
            longitude: currentLocation.longitude,
            distance: distance,
          );
        }
      }
    });
  }

  void _onSearchChanged(String value) {
    _lastSearch = value;
    _debounceTimer?.cancel();

    _debounceTimer = Timer(const Duration(milliseconds: 500), () async {
      // Only call search if the value hasn't changed during debounce
      if (_searchController.text == _lastSearch) {
        final viewModel = ref.read(shopsViewModelProvider.notifier);
        viewModel.updateSearchQueryString(value);
      }
    });
  }

  @override
  void didUpdateWidget(covariant SearchShopTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Do not reset _searchController.text here
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(shopsViewModelProvider);
    final viewModel = ref.watch(shopsViewModelProvider.notifier);
    final isLoading = ref.watch(shopsViewModelProvider).isLoading;

    // Fix: Ensure state.branchCount and state.radius are not null
    final branchCount = state.branchCount;
    final radius = state.radius;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha((0.1 * 255).round()),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 50,
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      hintText: 'Search for products...',
                      hintStyle: TextStyle(color: Colors.grey, fontSize: 16),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(horizontal: 16),
                    ),
                    style: const TextStyle(fontSize: 16),
                    onChanged: _onSearchChanged,
                  ),
                ),
                state.isSearchingProduct == true
                    ? Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0),
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.orange,
                          ),
                        ),
                      ),
                    )
                    : IconButton(
                      icon: const Icon(Icons.search, color: Colors.grey),
                      onPressed: () {},
                    ),
              ],
            ),
          ),
          Divider(
            height: 1,
            thickness: 0.5,
            color: Colors.grey.withAlpha((0.3 * 255).round()),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (!state.isAdjusting) ...[
                  Text(
                    branchCount > 0
                        ? 'Showing $branchCount hardware ${branchCount > 1 ? 'shops' : 'shop'} within ${radius}km'
                        : 'No shops found so far with a radius of $radius',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                  TextButton(
                    onPressed: () => viewModel.setAdjustingRadiusState(true),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: Text(
                      'Adjust',
                      style: TextStyle(
                        color: Colors.blue[700],
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ] else
                  Expanded(
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _distanceController,
                            keyboardType: TextInputType.number,
                            onChanged: _onDistanceChanged,
                            decoration: InputDecoration(
                              isDense: true,
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 0,
                                vertical: 8,
                              ),
                              border: InputBorder.none,
                              hintText: 'Enter distance in km',
                              suffixText: state.isLoading ? '' : 'km',
                              suffixStyle: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                            style: const TextStyle(fontSize: 12),
                          ),
                        ),
                        SizedBox(
                          width: 16,
                          height: 16,
                          child:
                              isLoading
                                  ? CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.blue[700]!,
                                    ),
                                  )
                                  : IconButton(
                                    icon: const Icon(Icons.close, size: 16),
                                    padding: EdgeInsets.zero,
                                    constraints: const BoxConstraints(),
                                    onPressed: () {
                                      _distanceController.text =
                                          radius.toString();
                                      viewModel.setAdjustingRadiusState(false);

                                      // _onDistanceChanged('10');
                                    },
                                  ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

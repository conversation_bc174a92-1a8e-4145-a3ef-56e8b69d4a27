import 'package:build_mate/presentation/components/helper_widgets/spacing_widgets.dart';
import 'package:build_mate/presentation/screens/home/<USER>/tabs/shops_components/shop_details_sheet.dart';
import 'package:build_mate/presentation/view_models/user/shops_view_model.dart';
import 'package:build_mate/presentation/screens/home/<USER>/tabs/shops_components/search_shop_textfield.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shimmer/shimmer.dart';

class ShopsListBottomSheet extends ConsumerWidget {
  const ShopsListBottomSheet({super.key});
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(shopsViewModelProvider);
    // final viewModel = ref.watch(shopsViewModelProvider.notifier);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            IconButton(
              icon: Icon(Icons.close, color: Colors.grey[700]),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
        vSpace(16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: const SearchShopTextField(),
        ),
        Expanded(
          child:
              state.isLoading
                  ? ListView.builder(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    itemCount: 6,
                    itemBuilder: (context, index) {
                      return Container(
                        height: 100,
                        margin: const EdgeInsets.symmetric(
                          vertical: 4,
                          horizontal: 8,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withAlpha((0.06 * 255).round()),
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            Shimmer.fromColors(
                              baseColor: Colors.grey[300]!,
                              highlightColor: Colors.grey[100]!,
                              child: Container(
                                width: 90,
                                height: 90,
                                color: Colors.grey[300],
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Shimmer.fromColors(
                                    baseColor: Colors.grey[300]!,
                                    highlightColor: Colors.grey[100]!,
                                    child: Container(
                                      width: 120,
                                      height: 16,
                                      color: Colors.grey[300],
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Shimmer.fromColors(
                                    baseColor: Colors.grey[300]!,
                                    highlightColor: Colors.grey[100]!,
                                    child: Container(
                                      width: 80,
                                      height: 12,
                                      color: Colors.grey[300],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Shimmer.fromColors(
                              baseColor: Colors.grey[300]!,
                              highlightColor: Colors.grey[100]!,
                              child: Container(
                                width: 40,
                                height: 16,
                                color: Colors.grey[300],
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  )
                  : ListView.separated(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    itemCount: state.nearbyShops.length,
                    separatorBuilder:
                        (_, __) => Divider(
                          height: 1,
                          color: Colors.grey.withAlpha((0.15 * 255).round()),
                        ),
                    itemBuilder: (context, index) {
                      final shop = state.nearbyShops[index];
                      final imageUrl =
                          (shop.hardwareShop?.images != null &&
                                  shop.hardwareShop!.images!.isNotEmpty)
                              ? shop.hardwareShop!.images!.first
                              : null;
                      return GestureDetector(
                        onTap: () {
                          ref
                              .read(shopsViewModelProvider.notifier)
                              .selectShop(shop);
                          showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            backgroundColor: Colors.transparent,
                            builder:
                                (_) => ShopDetailsSheet(() {
                                  Navigator.of(context).pop();
                                  ref
                                      .read(shopsViewModelProvider.notifier)
                                      .setShowdetailsVisibility(false);
                                  ref
                                      .read(shopsViewModelProvider.notifier)
                                      .selectShop(null);
                                }),
                          );
                        },
                        child: Container(
                          height: 100,
                          margin: const EdgeInsets.symmetric(
                            vertical: 4,
                            horizontal: 8,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withAlpha((0.06 * 255).round()),
                                blurRadius: 4,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child:
                                    imageUrl != null
                                        ? Image.network(
                                          imageUrl,
                                          width: 90,
                                          height: 90,
                                          fit: BoxFit.cover,
                                          errorBuilder:
                                              (context, error, stackTrace) =>
                                                  Container(
                                                    width: 90,
                                                    height: 90,
                                                    color: Colors.grey[200],
                                                    child: Icon(
                                                      Icons.store,
                                                      color: Colors.grey[400],
                                                    ),
                                                  ),
                                        )
                                        : Container(
                                          width: 90,
                                          height: 90,
                                          color: Colors.grey[200],
                                          child: Icon(
                                            Icons.store,
                                            color: Colors.grey[400],
                                          ),
                                        ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      shop.hardwareShop?.name ?? '',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.w600,
                                        fontSize: 16,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      shop.name ?? '',
                                      style: const TextStyle(
                                        fontSize: 13,
                                        color: Colors.grey,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    shop.distanceKm != null
                                        ? '${shop.distanceKm!.toStringAsFixed(1)} km'
                                        : '',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                      fontSize: 14,
                                      color: Colors.black87,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
        ),
      ],
    );
  }
}

import 'package:build_mate/presentation/screens/home/<USER>/tabs/shops_components/search_product_textfield.dart';
import 'package:build_mate/presentation/view_models/user/shop_products_view_model.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shimmer/shimmer.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'product_card.dart';

class ShopProductsView extends ConsumerStatefulWidget {
  const ShopProductsView({super.key});

  @override
  ConsumerState<ShopProductsView> createState() => _ShopProductViewState();
}

class _ShopProductViewState extends ConsumerState<ShopProductsView> {
  void _showProductBottomSheet(BuildContext context, product) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (_) {
        final imageUrl =
            (product.productImages != null && product.productImages!.isNotEmpty)
                ? product.productImages!.first.imageUrl ?? ''
                : '';
        return Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (imageUrl.isNotEmpty)
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.network(
                    imageUrl,
                    height: 160,
                    width: double.infinity,
                    fit: BoxFit.cover,
                  ),
                ),
              const SizedBox(height: 16),
              Text(
                product.name ?? '',
                style: MyTypography.Medium.copyWith(fontSize: 20),
              ),
              if (product.hardwareSubCategory?.name != null)
                Text(
                  product.hardwareSubCategory!.name!,
                  style: MyTypography.Light.copyWith(fontSize: 16),
                ),
              const SizedBox(height: 8),
              if (product.price != null)
                Text(
                  '\$${product.price!.toStringAsFixed(2)}',
                  style: MyTypography.Medium.copyWith(
                    fontSize: 18,
                    color: Colors.green[700],
                  ),
                ),
              if (product.description != null)
                Padding(
                  padding: const EdgeInsets.only(top: 12.0),
                  child: Text(
                    product.description!,
                    style: MyTypography.Light.copyWith(fontSize: 14),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(shopProductsViewModelProvider);
    // final viewModel = ref.watch(shopProductsViewModelProvider.notifier);
    return Scaffold(
      backgroundColor: Colors.white, // Set background to white
      appBar: AppBar(
        backgroundColor: orangeColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Colors.white, size: 20),
          onPressed: () {},
        ),
        title: Column(
          children: [
            Text(
              state.shopname,
              style: MyTypography.Medium.copyWith(
                color: Colors.white,
                fontSize: 18,
              ),
            ),
            Text(
              state.branchName,
              style: MyTypography.Light.copyWith(
                color: Colors.white.withAlpha((0.85 * 255).round()),
                fontSize: 14,
              ),
            ),
          ],
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(Icons.favorite_border, color: Colors.white),
            onPressed: () {},
          ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 16),
            // Search Bar
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withAlpha((0.08 * 255).round()),
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: const SearchProductTextField(),
            ),
            SizedBox(height: 24),
            // Products Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  state.selectedSubcategoryName ?? 'All Products',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                Row(
                  children: [
                    const SizedBox(width: 8),
                    if (state.selectedSubcategoryId != null)
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey[200],
                          foregroundColor: Colors.black,
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          textStyle: const TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                        onPressed: () async {
                          await ref
                              .read(shopProductsViewModelProvider.notifier)
                              .resetFilter();
                        },
                        child: Row(
                          children: [
                            Icon(Icons.refresh, size: 18, color: Colors.orange),
                            const SizedBox(width: 6),
                            Text('Reset'),
                          ],
                        ),
                      ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 16),
            // Products Grid or Skeleton
            Expanded(
              child:
                  state.isLoadingProducts
                      ? GridView.builder(
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 16,
                          childAspectRatio: 0.75,
                        ),
                        itemCount: 6,
                        itemBuilder: (context, index) {
                          return Shimmer.fromColors(
                            baseColor: Colors.grey[300]!,
                            highlightColor: Colors.grey[100]!,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.grey[300],
                                borderRadius: BorderRadius.circular(16),
                              ),
                              margin: EdgeInsets.symmetric(vertical: 4),
                            ),
                          );
                        },
                      )
                      : state.products.isEmpty
                      ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SvgPicture.asset(
                              'assets/svg/empty_cart.svg',
                              width: 120,
                              height: 120,
                            ),
                            const SizedBox(height: 24),
                            Text(
                              'No products found',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey[700],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      )
                      : GridView.builder(
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 16,
                          childAspectRatio: 0.75,
                        ),
                        itemCount: state.products.length,
                        itemBuilder: (context, index) {
                          final product = state.products[index];
                          final imageUrl =
                              (product.productImages != null &&
                                      product.productImages!.isNotEmpty)
                                  ? product.productImages!.first.imageUrl ?? ''
                                  : '';
                          return GestureDetector(
                            onTap:
                                () => _showProductBottomSheet(context, product),
                            child: ProductCard(
                              imageUrl: imageUrl,
                              title: product.name ?? '',
                              subtitle: product.hardwareSubCategory?.name ?? '',
                              price:
                                  product.price != null
                                      ? '\$${product.price!.toStringAsFixed(2)}'
                                      : '',
                              isPremium: false,
                              backgroundColor: Colors.white,
                            ),
                          );
                        },
                      ),
            ),
          ],
        ),
      ),
    );
  }
}

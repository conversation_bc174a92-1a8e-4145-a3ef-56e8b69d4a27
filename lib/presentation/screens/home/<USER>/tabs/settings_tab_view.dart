import 'package:flutter/material.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:go_router/go_router.dart';

class SettingsTabView extends StatelessWidget {
  const SettingsTabView({super.key});

  Widget _buildSettingsGroup({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            title.toUpperCase(),
            style: MyTypography.SemiBold.copyWith(
              color: Colors.grey[600],
              fontSize: 13,
              letterSpacing: 0.5,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(children: children),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    Widget? trailing,
    VoidCallback? onTap,
    Color? iconColor,
  }) {
    return ListTile(
      leading: Icon(icon, color: iconColor ?? Colors.grey[700], size: 22),
      title: Text(
        title,
        style: MyTypography.Regular.copyWith(
          fontSize: 16,
          color: Colors.grey[800],
        ),
      ),
      trailing:
          trailing ??
          Icon(Icons.chevron_right, color: Colors.grey[400], size: 20),
      onTap: onTap,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: 200.0,
            floating: false,
            pinned: true,
            backgroundColor: darkBlueColor,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.white),
              onPressed: () => context.pop(),
            ),
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                color: darkBlueColor,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(height: 40), // Space for status bar
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                          image: const DecorationImage(
                            image: AssetImage(
                              'assets/images/profile_pic.png',
                            ),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'Saydul Moon',
                        style: MyTypography.SemiBold.copyWith(
                          fontSize: 20,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green.withAlpha(51), // 0.2 opacity
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'Online',
                          style: MyTypography.Medium.copyWith(
                            fontSize: 12,
                            color: Colors.green,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Column(
              children: [
                const SizedBox(height: 24),
                _buildSettingsGroup(
                  title: 'Account',
                  children: [
                       _buildSettingsTile(
                      icon: Icons.person_off_outlined,
                      title: 'Availability',
                      trailing: Switch(
                        value: false,
                        onChanged: (value) {},
                        activeColor: Colors.blue,
                      ),
                    ),
                    Divider(height: 1, color: Colors.grey[200]),
                    _buildSettingsTile(
                      icon: Icons.account_circle_outlined,
                      title: 'Account details',
                      onTap: () {},
                    ),
                     Divider(height: 1, color: Colors.grey[200]),
                    _buildSettingsTile(
                      icon: Icons.attach_money,
                      title: 'Subscriptions',
                      onTap: () {},
                    ),
                  ],
                ),
                _buildSettingsGroup(
                  title: 'PREFERENCES',
                  children: [
                    _buildSettingsTile(
                      icon: Icons.language,
                      title: 'Language',
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'English',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Icon(
                            Icons.chevron_right,
                            color: Colors.grey[400],
                            size: 20,
                          ),
                        ],
                      ),
                      onTap: () {},
                    ),
                    Divider(height: 1, color: Colors.grey[200]),
                    _buildSettingsTile(
                      icon: Icons.dark_mode_outlined,
                      title: 'Dark Mode',
                      trailing: Switch(
                        value: false,
                        onChanged: (value) {},
                        activeColor: Colors.blue,
                      ),
                    ),
                    Divider(height: 1, color: Colors.grey[200]),
                    _buildSettingsTile(
                      icon: Icons.notifications,
                      title: 'Notifications',
                      trailing: Switch(
                        value: true,
                        onChanged: (value) {},
                        activeColor: Colors.blue,
                      ),
                    ),
                    Divider(height: 1, color: Colors.grey[200]),
                    _buildSettingsTile(
                      icon: Icons.play_circle_outline,
                      title: 'Play in Background',
                      trailing: Switch(
                        value: false,
                        onChanged: (value) {},
                        activeColor: Colors.blue,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

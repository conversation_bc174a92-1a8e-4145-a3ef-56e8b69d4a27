import 'package:build_mate/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

final homeNavigationIndexProvider = StateProvider<int>((ref) => 0);

class BottomNavBar extends HookConsumerWidget {
  const BottomNavBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.watch(homeNavigationIndexProvider);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(color: Colors.black.withAlpha(13), blurRadius: 10),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildNavItem(
                context,
                ref,
                0,
                Icons.home_outlined,
                Icons.home,
                'Home',
              ),
              _buildNavItem(
                context,
                ref,
                1,
                Icons.description_outlined,
                Icons.description,
                'Jobs',
              ),
              _buildNavItem(
                context,
                ref,
                2,
                Icons.work_outline,
                Icons.work,
                'Messages',
              ),
              _buildNavItem(
                context,
                ref,
                3,
                Icons.store_outlined,
                Icons.store,
                'Shop',
              ),
              _buildNavItem(
                context,
                ref,
                4,
                Icons.person_outline,
                Icons.person,
                'Profile',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(
    BuildContext context,
    WidgetRef ref,
    int index,
    IconData outlinedIcon,
    IconData filledIcon,
    String label,
  ) {
    final currentIndex = ref.watch(homeNavigationIndexProvider);
    final isSelected = currentIndex == index;
    final color = isSelected ? orangeColor : Colors.grey[600];

    return InkWell(
      onTap: () => ref.read(homeNavigationIndexProvider.notifier).state = index,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 3,
            width: 30,
            decoration: BoxDecoration(
              color: isSelected ? orangeColor : Colors.transparent,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 4),
          Icon(isSelected ? filledIcon : outlinedIcon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            label,
            style: MyTypography.Medium.copyWith(color: color, fontSize: 12),
          ),
        ],
      ),
    );
  }
}

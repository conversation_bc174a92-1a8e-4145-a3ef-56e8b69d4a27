import 'dart:io';
import 'dart:convert';
import 'package:build_mate/presentation/routes/route_config.dart';
import 'package:build_mate/presentation/state/post_job_flow_state.dart';
import 'package:build_mate/presentation/view_models/user/all_services_view_model.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:go_router/go_router.dart';

// Add this to track upload progress for each image
class ImageUploadProgress {
  final File file;
  final double progress;
  final bool isUploading;
  final String? error;
  final String? url;

  ImageUploadProgress({
    required this.file,
    this.progress = 0.0,
    this.isUploading = false,
    this.error,
    this.url,
  });

  ImageUploadProgress copyWith({
    File? file,
    double? progress,
    bool? isUploading,
    String? error,
    String? url,
  }) {
    return ImageUploadProgress(
      file: file ?? this.file,
      progress: progress ?? this.progress,
      isUploading: isUploading ?? this.isUploading,
      error: error,
      url: url ?? this.url,
    );
  }
}

final postJobViewModelProvider =
    StateNotifierProvider<PostJobViewModel, PostJobFlowState>((ref) {
      return PostJobViewModel(
        PostJobFlowState(),
        ref.read(goRouterProvider),
        ref,
      );
    });

class PostJobViewModel extends StateNotifier<PostJobFlowState> {
  final GoRouter _router;
  final supabase = Supabase.instance.client;
  final ImagePicker _picker = ImagePicker();
  final Ref _ref;

  // Form controllers
  final descriptionController = TextEditingController();
  final budgetController = TextEditingController();

  // Maps to store service and subcategory IDs
  Map<String, int> serviceIdMap = {};
  Map<String, Map<String, int>> subcategoryIdMap = {};
  Map<String, List<String>> categoriesMap = {};

  // Track upload progress for each image
  Map<int, ImageUploadProgress> imageUploadProgress = {};

  PostJobViewModel(super.state, this._router, this._ref) {
    // Get the service ID first
    final serviceId = getServiceId();
    if (kDebugMode) {
      print('Initial service ID from AllServicesViewModel: $serviceId');
    }

    // Load categories and then select the appropriate category
    _loadCategories();
  }

  // Add this method to get the service ID from AllServicesViewModel
  int getServiceId() {
    return _ref.read(allServicesViewModelProvider).selectedServiceId;
  }

  Future<void> _loadCategories() async {
    try {
      state = state.copyWith(isLoading: true);

      // Try to get categories from storage first
      final categories = await _getCategoriesFromStorage();

      // If we have categories, we're done
      if (categories.isNotEmpty) {
        if (kDebugMode) {
          print('Loaded ${categories.length} categories from storage');
        }
        state = state.copyWith(isLoading: false);

        // After loading categories, select the category based on service ID
        selectCategoryFromServiceId();
        return;
      }

      // If no categories in storage, fetch them from the database
      await _fetchServicesAndSubcategories();

      state = state.copyWith(isLoading: false);

      // After fetching categories, select the category based on service ID
      selectCategoryFromServiceId();
    } catch (e) {
      if (kDebugMode) {
        print('Error loading categories: $e');
      }
      state = state.copyWith(isLoading: false);
    }
  }

  Future<List<Map<String, dynamic>>> _getCategoriesFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final categoriesJson = prefs.getString('categories');

      if (categoriesJson != null) {
        final categoriesData = jsonDecode(categoriesJson);

        // Load service ID map
        if (categoriesData.containsKey('serviceIdMap')) {
          serviceIdMap = Map<String, int>.from(
            categoriesData['serviceIdMap'].map(
              (key, value) => MapEntry(key.toString(), value as int),
            ),
          );
        }

        // Load subcategory ID map
        if (categoriesData.containsKey('subcategoryIdMap')) {
          final subCatMap = categoriesData['subcategoryIdMap'];
          subcategoryIdMap = {};

          subCatMap.forEach((key, value) {
            subcategoryIdMap[key.toString()] = Map<String, int>.from(
              value.map((k, v) => MapEntry(k.toString(), v as int)),
            );
          });
        }

        return List<Map<String, dynamic>>.from(categoriesData['categories']);
      }

      return [];
    } catch (e) {
      if (kDebugMode) {
        print('Error getting categories from storage: $e');
      }
      return [];
    }
  }

  Future<void> _fetchServicesAndSubcategories() async {
    try {
      // Fetch all services
      final servicesResponse = await supabase
          .from('services')
          .select('id, name')
          .order('name');

      if (servicesResponse.isEmpty) {
        if (kDebugMode) {
          print('No services found');
        }

        // If no services found, use fallback categories
        _useFallbackCategories();
        return;
      }

      final List<Map<String, dynamic>> services =
          List<Map<String, dynamic>>.from(servicesResponse);

      if (kDebugMode) {
        print('Fetched ${services.length} services from database');
      }

      // Clear existing maps
      serviceIdMap.clear();
      subcategoryIdMap.clear();
      categoriesMap.clear();

      // For each service, fetch its subcategories
      for (final service in services) {
        final int serviceId = service['id'];
        final String serviceName = service['name'];

        // Store service ID mapping
        serviceIdMap[serviceName] = serviceId;

        // Initialize subcategory ID map for this service
        subcategoryIdMap[serviceName] = {};

        // Fetch subcategories for this service
        final subcategoriesResponse = await supabase
            .from('sub_categories')
            .select('id, name')
            .eq('service_id', serviceId)
            .order('name');

        if (subcategoriesResponse.isNotEmpty) {
          final List<dynamic> subcategories = List<dynamic>.from(
            subcategoriesResponse,
          );
          final List<String> subcategoryNames = [];

          // Store subcategory names and their IDs
          for (final subcategory in subcategories) {
            final String subcategoryName = subcategory['name'];
            final int subcategoryId = subcategory['id'];

            subcategoryNames.add(subcategoryName);
            subcategoryIdMap[serviceName]![subcategoryName] = subcategoryId;
          }

          // Add to our categories map
          categoriesMap[serviceName] = subcategoryNames;

          if (kDebugMode) {
            print('Service: $serviceName, Subcategories: $subcategoryNames');
          }
        } else {
          // Even if there are no subcategories, add the service with an empty list
          categoriesMap[serviceName] = [];

          if (kDebugMode) {
            print('Service: $serviceName has no subcategories');
          }
        }
      }

      // Save to shared preferences
      _saveCategoriesInStorage();

      // Debug log all categories and subcategories
      if (kDebugMode) {
        print('All categories and subcategories:');
        for (final entry in categoriesMap.entries) {
          print('${entry.key}: ${entry.value}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching services and subcategories: $e');
      }

      // Use fallback categories on error
      _useFallbackCategories();
    }
  }

  // Add a method to use fallback categories when database fetch fails
  void _useFallbackCategories() {
    try {
      // Clear existing maps
      serviceIdMap.clear();
      subcategoryIdMap.clear();
      categoriesMap.clear();

      // Use the fallback categories from the JSON file
      rootBundle.loadString('assets/json/categories.json').then((jsonString) {
        final data = json.decode(jsonString);
        final categories = List<Map<String, dynamic>>.from(data['categories']);

        int serviceId = 1; // Start with ID 1 for fallback categories

        for (final category in categories) {
          final String categoryName = category['name'];
          final List<dynamic> subcategoriesData =
              category['subcategories'] ?? [];
          final List<String> subcategories = List<String>.from(
            subcategoriesData,
          );

          // Store service ID mapping
          serviceIdMap[categoryName] = serviceId;

          // Initialize subcategory ID map for this service
          subcategoryIdMap[categoryName] = {};

          // Store subcategory names and their IDs
          int subcategoryId = 1;
          for (final subcategory in subcategories) {
            subcategoryIdMap[categoryName]![subcategory] = subcategoryId++;
          }

          // Add to our categories map
          categoriesMap[categoryName] = subcategories;

          serviceId++; // Increment service ID for next category
        }

        // Save to shared preferences
        _saveCategoriesInStorage();

        // Debug log all categories and subcategories
        if (kDebugMode) {
          print('Loaded fallback categories:');
          for (final entry in categoriesMap.entries) {
            print('${entry.key}: ${entry.value}');
          }
        }

        // After loading fallback categories, select the category based on service ID
        selectCategoryFromServiceId();
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error loading fallback categories: $e');
      }
    }
  }

  // Add this method to select the category based on service ID
  void selectCategoryFromServiceId() {
    try {
      final int serviceId = getServiceId();

      if (serviceId <= 0) {
        if (kDebugMode) {
          print('No service ID selected, skipping category selection');
        }
        return;
      }

      if (kDebugMode) {
        print('Selecting category for service ID: $serviceId');
        print('Available service IDs: $serviceIdMap');
      }

      // Find the main category name that corresponds to the service ID
      String? mainCategoryName;
      for (final entry in serviceIdMap.entries) {
        if (entry.value == serviceId) {
          mainCategoryName = entry.key;
          break;
        }
      }

      if (mainCategoryName == null) {
        if (kDebugMode) {
          print('No main category found for service ID: $serviceId');
          print('Trying to load from JSON file...');
        }

        // If no main category found, try to load from JSON file
        _loadCategoryFromJson(serviceId);
        return;
      }

      if (kDebugMode) {
        print(
          'Found main category: $mainCategoryName for service ID: $serviceId',
        );
      }

      // Get the subcategories for this main category
      List<String> subCategories = categoriesMap[mainCategoryName] ?? [];

      if (subCategories.isEmpty) {
        if (kDebugMode) {
          print(
            'No subcategories found for $mainCategoryName, trying to load from JSON file...',
          );
        }

        // If no subcategories found, try to load from JSON file
        _loadSubcategoriesFromJson(mainCategoryName);

        // Get the subcategories again after loading from JSON
        subCategories = categoriesMap[mainCategoryName] ?? [];
      }

      // Update the state with the selected main category and related subcategories
      state = state.copyWith(
        selectedMainCategory: mainCategoryName,
        selectedMainCategoryId: serviceId,
        relatedSubCategories: subCategories,
      );

      if (kDebugMode) {
        print('Updated state with main category: $mainCategoryName');
        print('Related subcategories: $subCategories');
      }

      // If there are subcategories, select the first one by default
      if (subCategories.isNotEmpty) {
        state = state.copyWith(selectedSubCategories: [subCategories.first]);

        if (kDebugMode) {
          print('Selected first subcategory: ${subCategories.first}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error selecting category from service ID: $e');
      }
    }
  }

  Future<void> onCategorySelected(
    String mainCategory,
    String subCategory,
    List<String> subCategories,
  ) async {
    try {
      if (kDebugMode) {
        print(
          'onCategorySelected called with: mainCategory=$mainCategory, subCategory=$subCategory',
        );
      }

      // Get the service ID and subcategory ID from shared preferences
      final prefs = await SharedPreferences.getInstance();
      final categoriesJson = prefs.getString('categories');
      int serviceId = 0;
      Map<String, int> subcategoryIdMap = {};

      if (categoriesJson != null) {
        final categoriesData = jsonDecode(categoriesJson);

        // Safely extract the service ID map
        if (categoriesData.containsKey('serviceIdMap')) {
          final serviceIdMap = Map<String, dynamic>.from(
            categoriesData['serviceIdMap'],
          );
          if (kDebugMode) {
            print('Service ID Map from shared preferences: $serviceIdMap');
          }

          if (serviceIdMap.containsKey(mainCategory)) {
            serviceId = serviceIdMap[mainCategory];
            if (kDebugMode) {
              print('Found service ID for $mainCategory: $serviceId');
            }
          } else {
            if (kDebugMode) {
              print('Service $mainCategory not found in serviceIdMap');
            }
            // If not found in shared preferences, fetch from database
            await _fetchServicesAndSubcategories();
            return; // This will restart the process with updated shared preferences
          }
        } else {
          if (kDebugMode) {
            print('serviceIdMap not found in shared preferences');
          }
          // If serviceIdMap not found, fetch all services and subcategories
          await _fetchServicesAndSubcategories();
          return; // This will restart the process with updated shared preferences
        }

        // Safely extract the subcategory ID map
        if (categoriesData.containsKey('subcategoryIdMap')) {
          final allSubcategoryIdMaps = categoriesData['subcategoryIdMap'];
          if (allSubcategoryIdMaps != null &&
              allSubcategoryIdMaps is Map &&
              allSubcategoryIdMaps.containsKey(mainCategory)) {
            // Convert the subcategory map to the correct type
            final subMap = allSubcategoryIdMaps[mainCategory];
            if (subMap != null && subMap is Map) {
              subcategoryIdMap = Map<String, int>.from(
                subMap.map(
                  (key, value) => MapEntry(key.toString(), value as int),
                ),
              );
              if (kDebugMode) {
                print(
                  'Found subcategory ID map for $mainCategory: $subcategoryIdMap',
                );
              }
            }
          }
        }
      } else {
        if (kDebugMode) {
          print(
            'No categories found in shared preferences, fetching from database',
          );
        }
        await _fetchServicesAndSubcategories();
        return; // This will restart the process with updated shared preferences
      }

      // First, set the state with the selected category and subcategory
      state = state.copyWith(
        selectedMainCategory: mainCategory,
        selectedMainCategoryId: serviceId,
        selectedSubCategories: [subCategory], // Initialize with first selection
        relatedSubCategories: subCategories,
        subcategoryIdMap: subcategoryIdMap,
      );

      // If the subcategories list is empty or service ID is 0, try to fetch them from the database
      if (subCategories.isEmpty || serviceId == 0) {
        await _fetchServiceAndSubcategoriesFromDatabase(
          mainCategory,
          subCategory,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error in onCategorySelected: $e');
      }
      state = state.copyWith(isLoading: false);
    }
  }

  Future<void> _fetchServiceAndSubcategoriesFromDatabase(
    String mainCategory,
    String subCategory,
  ) async {
    try {
      state = state.copyWith(isLoading: true);

      // Find the service ID for the selected main category
      final serviceResponse =
          await supabase
              .from('services')
              .select('id')
              .eq('name', mainCategory)
              .maybeSingle();

      if (serviceResponse != null) {
        final int fetchedServiceId = serviceResponse['id'];
        if (kDebugMode) {
          print('Fetched service ID from database: $fetchedServiceId');
        }

        // Update the state with the fetched service ID
        state = state.copyWith(selectedMainCategoryId: fetchedServiceId);

        // Fetch subcategories for this service
        final subcategoriesResponse = await supabase
            .from('sub_categories')
            .select('id, name')
            .eq('service_id', fetchedServiceId)
            .order('name');

        if (subcategoriesResponse.isEmpty) {
          final List<dynamic> fetchedSubcategories = List<dynamic>.from(
            subcategoriesResponse,
          );
          final List<String> subcategoryNames =
              fetchedSubcategories
                  .map((subcategory) => subcategory['name'] as String)
                  .toList();

          // Create subcategory ID map
          final Map<String, int> newSubcategoryIdMap = {};
          for (final subcategory in fetchedSubcategories) {
            newSubcategoryIdMap[subcategory['name']] = subcategory['id'];
          }

          // Update the state with the fetched subcategories and ID map
          state = state.copyWith(
            relatedSubCategories: subcategoryNames,
            subcategoryIdMap: newSubcategoryIdMap,
            isLoading: false,
          );

          // Update shared preferences with this new data
          _updateSharedPreferencesWithNewData(
            mainCategory,
            fetchedServiceId,
            newSubcategoryIdMap,
          );
        }
      }

      state = state.copyWith(isLoading: false);
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching service and subcategories from database: $e');
      }
      state = state.copyWith(isLoading: false);
    }
  }

  Future<void> _updateSharedPreferencesWithNewData(
    String mainCategory,
    int serviceId,
    Map<String, int> subcategoryIdMap,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final categoriesJson = prefs.getString('categories');

      if (categoriesJson != null) {
        final categoriesData = jsonDecode(categoriesJson);

        // Update the service ID map
        if (categoriesData.containsKey('serviceIdMap')) {
          final serviceIdMap = Map<String, dynamic>.from(
            categoriesData['serviceIdMap'],
          );
          serviceIdMap[mainCategory] = serviceId;
          categoriesData['serviceIdMap'] = serviceIdMap;
        }

        // Update the subcategory ID map
        if (categoriesData.containsKey('subcategoryIdMap')) {
          final allSubcategoryIdMaps = Map<String, dynamic>.from(
            categoriesData['subcategoryIdMap'],
          );
          allSubcategoryIdMaps[mainCategory] = subcategoryIdMap;
          categoriesData['subcategoryIdMap'] = allSubcategoryIdMaps;
        }

        // Save updated data back to shared preferences
        await prefs.setString('categories', jsonEncode(categoriesData));
        if (kDebugMode) {
          print('Updated shared preferences with new data for $mainCategory');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating shared preferences with new data: $e');
      }
    }
  }

  void setSelectedMainCategory(String mainCategory) {
    state = state.copyWith(selectedMainCategory: mainCategory);
  }

  void addSubCategory(String subCategory) {
    final updatedSubCategories = [...state.selectedSubCategories, subCategory];
    state = state.copyWith(selectedSubCategories: updatedSubCategories);
  }

  void removeSubCategory(String subCategory) {
    final updatedSubCategories = [...state.selectedSubCategories];
    updatedSubCategories.remove(subCategory);
    state = state.copyWith(selectedSubCategories: updatedSubCategories);
  }

  void setRelatedSubCategories(List<String> subCategories) {
    state = state.copyWith(relatedSubCategories: subCategories);
  }

  void updateDescription(String description) {
    state = state.copyWith(description: description);
  }

  void updateBudget(String budgetText) {
    try {
      final budget = double.parse(budgetText);
      state = state.copyWith(budget: budget.toString());
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing budget: $e');
      }
    }
  }

  void updateServiceDate(DateTime date) {
    state = state.copyWith(serviceDate: date);
  }

  Future<void> pickImage(int index) async {
    try {
      // Request permission
      final status = await Permission.photos.request();

      if (status.isGranted) {
        final XFile? pickedFile = await _picker.pickImage(
          source: ImageSource.gallery,
          imageQuality: 80,
        );

        if (pickedFile != null) {
          final File imageFile = File(pickedFile.path);

          // Update job image files
          final List<File> updatedFiles = [...state.jobImageFiles];
          if (index < updatedFiles.length) {
            updatedFiles[index] = imageFile;
          } else {
            updatedFiles.add(imageFile);
          }

          state = state.copyWith(jobImageFiles: updatedFiles);

          // Initialize upload progress tracking
          imageUploadProgress[index] = ImageUploadProgress(
            file: imageFile,
            isUploading: false,
          );

          // Start uploading immediately in background
          _uploadSingleImage(index, imageFile);
        }
      } else {
        if (kDebugMode) {
          print('Permission not granted: $status');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error picking image: $e');
      }
    }
  }

  Future<void> _uploadSingleImage(int index, File imageFile) async {
    try {
      // Set uploading state
      imageUploadProgress[index] = imageUploadProgress[index]!.copyWith(
        isUploading: true,
        progress: 0.0,
      );

      // Trigger UI update by creating a new state object
      // This is important for Freezed classes
      state = state.copyWith();

      final userId = supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileExtension = imageFile.path.split('.').last;
      final fileName = '$userId-job-$timestamp.$fileExtension';
      final filePath = 'job-images/$fileName';

      // Upload the file
      await supabase.storage
          .from('avatars')
          .upload(
            filePath,
            imageFile,
            fileOptions: const FileOptions(cacheControl: '3600', upsert: true),
          );

      // Simulate progress updates (since Supabase Flutter SDK doesn't have built-in progress)
      for (int i = 1; i <= 10; i++) {
        await Future.delayed(const Duration(milliseconds: 100));
        imageUploadProgress[index] = imageUploadProgress[index]!.copyWith(
          progress: i / 10,
        );

        // Trigger UI update with each progress update
        state = state.copyWith();
      }

      // Get the public URL
      final imageUrl = supabase.storage.from('avatars').getPublicUrl(filePath);

      // Update progress with completed status and URL
      imageUploadProgress[index] = imageUploadProgress[index]!.copyWith(
        isUploading: false,
        progress: 1.0,
        url: imageUrl,
      );

      // Update the state to trigger UI refresh
      state = state.copyWith();

      if (kDebugMode) {
        print('Image uploaded successfully: $imageUrl');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading image: $e');
      }

      // Update progress with error
      imageUploadProgress[index] = imageUploadProgress[index]!.copyWith(
        isUploading: false,
        error: e.toString(),
      );

      // Update the state to trigger UI refresh
      state = state.copyWith();

      // More detailed error logging
      if (e is StorageException) {
        if (kDebugMode) {
          print('Storage error code: ${e.statusCode}');
        }
        if (kDebugMode) {
          print('Storage error message: ${e.message}');
        }
        if (kDebugMode) {
          print('Storage error details: ${e.error}');
        }
      }
    }
  }

  void removeImage(int index) {
    final updatedFiles = [...state.jobImageFiles];
    if (index < updatedFiles.length) {
      updatedFiles.removeAt(index);

      // Remove from upload progress tracking
      imageUploadProgress.remove(index);

      // Reindex the remaining uploads
      final Map<int, ImageUploadProgress> newProgressMap = {};
      int newIndex = 0;

      for (var file in updatedFiles) {
        // Find the original index for this file
        final originalEntry = imageUploadProgress.entries.firstWhere(
          (entry) => entry.value.file.path == file.path,
          orElse: () => MapEntry(-1, ImageUploadProgress(file: file)),
        );

        if (originalEntry.key != -1) {
          newProgressMap[newIndex] = originalEntry.value;
        }
        newIndex++;
      }

      imageUploadProgress = newProgressMap;
      state = state.copyWith(jobImageFiles: updatedFiles);
    }
  }

  Future<List<String>> _uploadJobImages() async {
    final List<String> uploadedUrls = [];

    try {
      // Check if we already have uploaded URLs from background uploads
      for (int i = 0; i < state.jobImageFiles.length; i++) {
        if (imageUploadProgress.containsKey(i) &&
            imageUploadProgress[i]!.url != null) {
          // Image already uploaded, use the URL
          uploadedUrls.add(imageUploadProgress[i]!.url!);
        } else {
          // Image not uploaded yet, upload it now
          final userId = supabase.auth.currentUser?.id;
          if (userId == null) {
            throw Exception('User not authenticated');
          }

          final imageFile = state.jobImageFiles[i];
          final timestamp = DateTime.now().millisecondsSinceEpoch;
          final fileExtension = imageFile.path.split('.').last;
          final fileName = '$userId-job-$timestamp.$fileExtension';
          final filePath = 'job-images/$fileName';

          await supabase.storage
              .from('avatars')
              .upload(
                filePath,
                imageFile,
                fileOptions: const FileOptions(
                  cacheControl: '3600',
                  upsert: true,
                ),
              );

          final imageUrl = supabase.storage
              .from('avatars')
              .getPublicUrl(filePath);
          uploadedUrls.add(imageUrl);

          // Update progress tracking
          imageUploadProgress[i] = ImageUploadProgress(
            file: imageFile,
            isUploading: false,
            progress: 1.0,
            url: imageUrl,
          );
        }
      }

      return uploadedUrls;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading job images: $e');
      }
      rethrow;
    }
  }

  bool validateInputs(BuildContext context) {
    if (state.selectedMainCategory.isEmpty ||
        state.selectedSubCategories.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a service category and specialization'),
        ),
      );
      return false;
    }

    if (state.serviceDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a service date')),
      );
      return false;
    }

    if (state.budget.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a valid budget')),
      );
      return false;
    }

    if (state.description.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please provide a job description')),
      );
      return false;
    }

    return true;
  }

  Future<void> postJob(BuildContext context) async {
    if (!validateInputs(context)) {
      return;
    }

    try {
      state = state.copyWith(isSubmitting: true);

      // Update description and budget from controllers
      updateDescription(descriptionController.text.trim());
      updateBudget(budgetController.text.trim());

      // Upload images
      final List<String> imageUrls = await _uploadJobImages();

      // Get user ID
      final userId = supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      //Get client id from clients table
      final clientId = await supabase
          .from('clients')
          .select('id')
          .eq('supabase_id', userId)
          .single()
          .then((value) => value['id']);



      // Get subcategory IDs for selected subcategories
      final List<int> selectedSubCategoryIds = [];
      
      if (kDebugMode) {
        print('Selected subcategories: ${state.selectedSubCategories}');
        print('Subcategory ID map: ${state.subcategoryIdMap}');
      }

      for (final subCategory in state.selectedSubCategories) {
        if (state.subcategoryIdMap.containsKey(subCategory)) {
          selectedSubCategoryIds.add(state.subcategoryIdMap[subCategory]!);
        }
      }

      // Prepare data for the job post
      final Map<String, dynamic> postData = {
        'title': state.selectedSubCategories.isNotEmpty
            ? state.selectedSubCategories.first
            : 'General Job',
        'description': state.description,
        'budget': state.budget,
        'clientId': clientId,
        'serviceDate': state.serviceDate?.toIso8601String() ?? DateTime.now().toIso8601String(),
        'serviceId': state.selectedMainCategoryId > 0 ? state.selectedMainCategoryId : 1, // Use serviceId for service_id
        'subcategoryIds': selectedSubCategoryIds,
        'images': imageUrls,
      };

      if (kDebugMode) {
        print('Posting job with data: $postData');
      }

      //lets post the job by invoking a supabase function named post-job-function
      try {
        final response = await supabase.functions.invoke(
          'post-job-function',
          body: postData,
        );
        if (kDebugMode) {
          print('Response from edge function: ${response.data}');
        }

        // Set isSubmitting to false before showing UI feedback
        state = state.copyWith(isSubmitting: false);

        // IMPORTANT: Don't show snackbar or navigate here
        // Let the calling code handle the success UI
        return;
        
      } catch (functionError) {
        if (kDebugMode) {
          print('Error calling edge function: $functionError');
        }

        // Even if the function call fails, the data might still be saved
        // Set isSubmitting to false before showing UI feedback
        state = state.copyWith(isSubmitting: false);

        // IMPORTANT: Don't show snackbar or navigate here
        // Let the calling code handle the success UI
        return;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error posting job: $e');
      }
      state = state.copyWith(isSubmitting: false);

      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to post job: ${e.toString()}')),
        );
      }
      rethrow;
    }
  }

  // Add a method to check if an image is currently uploading
  bool isImageUploading(int index) {
    return imageUploadProgress.containsKey(index) &&
        imageUploadProgress[index]!.isUploading;
  }

  // Add a method to get upload progress for an image
  double getImageUploadProgress(int index) {
    if (imageUploadProgress.containsKey(index)) {
      return imageUploadProgress[index]!.progress;
    }
    return 0.0;
  }

  // Add a method to check if an image has an upload error
  String? getImageUploadError(int index) {
    if (imageUploadProgress.containsKey(index)) {
      return imageUploadProgress[index]!.error;
    }
    return null;
  }

  // Add a method to retry a failed upload
  void retryImageUpload(int index) {
    if (index < state.jobImageFiles.length) {
      _uploadSingleImage(index, state.jobImageFiles[index]);
    }
  }

  // Add this method to manually refresh the UI with the selected service
  void refreshWithSelectedService() {
    // Get the service ID
    final serviceId = getServiceId();

    if (kDebugMode) {
      print('Refreshing with service ID: $serviceId');
    }

    if (serviceId <= 0) {
      if (kDebugMode) {
        print('No service ID selected, skipping refresh');
      }
      return;
    }

    // Force update the category from the service ID
    updateCategoryFromServiceId();
  }

  // Add this method to set the selected service ID directly
  void setSelectedServiceId(int serviceId, String serviceName) {
    if (kDebugMode) {
      print('Setting selected service ID: $serviceId, name: $serviceName');
    }

    // Update the state with the new service ID and name
    state = state.copyWith(
      selectedMainCategoryId: serviceId,
      selectedMainCategory: serviceName,
    );

    // Also update the AllServicesViewModel to keep them in sync
    _ref.read(allServicesViewModelProvider.notifier).setSelectedService(serviceId, serviceName);

    if (kDebugMode) {
      print('Updated state: ${state.selectedMainCategory}, ${state.selectedMainCategoryId}');
    }

    // Force reload subcategories for this service
    loadSubcategoriesForService(serviceName);
  }

  // Add a method to load category from JSON file
  Future<void> _loadCategoryFromJson(int serviceId) async {
    try {
      final String jsonString = await rootBundle.loadString(
        'assets/json/categories.json',
      );
      final data = json.decode(jsonString);
      final categories = List<Map<String, dynamic>>.from(data['categories']);

      // Since we don't have a direct mapping from service ID to category in the JSON,
      // we'll use the index as a fallback
      if (serviceId > 0 && serviceId <= categories.length) {
        final category = categories[serviceId - 1];
        final String categoryName = category['name'];
        final List<dynamic> subcategoriesData = category['subcategories'] ?? [];
        final List<String> subcategories = List<String>.from(subcategoriesData);

        // Store service ID mapping
        serviceIdMap[categoryName] = serviceId;

        // Initialize subcategory ID map for this service
        subcategoryIdMap[categoryName] = {};

        // Store subcategory names and their IDs
        int subcategoryId = 1;
        for (final subcategory in subcategories) {
          subcategoryIdMap[categoryName]![subcategory] = subcategoryId++;
        }

        // Add to our categories map
        categoriesMap[categoryName] = subcategories;

        // Update the state with the selected main category and related subcategories
        state = state.copyWith(
          selectedMainCategory: categoryName,
          selectedMainCategoryId: serviceId,
          relatedSubCategories: subcategories,
        );

        if (kDebugMode) {
          print('Loaded category from JSON: $categoryName');
          print('Related subcategories: $subcategories');
        }

        // If there are subcategories, select the first one by default
        if (subcategories.isNotEmpty) {
          state = state.copyWith(selectedSubCategories: [subcategories.first]);

          if (kDebugMode) {
            print('Selected first subcategory: ${subcategories.first}');
          }
        }
      } else {
        if (kDebugMode) {
          print('Service ID $serviceId is out of range for JSON categories');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading category from JSON: $e');
      }
    }
  }

  // Add a method to load subcategories from JSON file
  Future<void> _loadSubcategoriesFromJson(String categoryName) async {
    try {
      final String jsonString = await rootBundle.loadString(
        'assets/json/categories.json',
      );
      final data = json.decode(jsonString);
      final categories = List<Map<String, dynamic>>.from(data['categories']);

      // Find the category in the JSON file
      for (final category in categories) {
        if (category['name'] == categoryName) {
          final List<dynamic> subcategoriesData =
              category['subcategories'] ?? [];
          final List<String> subcategories = List<String>.from(
            subcategoriesData,
          );

          // Initialize subcategory ID map for this service if not already initialized
          subcategoryIdMap[categoryName] ??= {};

          // Store subcategory names and their IDs
          int subcategoryId = 1;
          for (final subcategory in subcategories) {
            subcategoryIdMap[categoryName]![subcategory] = subcategoryId++;
          }

          // Add to our categories map
          categoriesMap[categoryName] = subcategories;

          // Update the state with the loaded subcategories
          state = state.copyWith(
            relatedSubCategories: subcategories,
            selectedSubCategories: subcategories.isNotEmpty ? [subcategories.first] : [],
          );

          if (kDebugMode) {
            print(
              'Loaded subcategories from JSON for $categoryName: $subcategories',
            );
            print('Updated state with subcategories: ${state.relatedSubCategories}');
            print('Selected first subcategory: ${state.selectedSubCategories}');
          }

          break;
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading subcategories from JSON: $e');
      }
    }
  }

  // Add a simple method to load subcategories for a given service
  Future<void> loadSubcategoriesForService(String serviceName) async {
    try {
      if (kDebugMode) {
        print('Loading subcategories for service: $serviceName');
      }

      // Get the service ID
      final serviceId = serviceIdMap[serviceName];
      if (serviceId == null) {
        if (kDebugMode) {
          print('Service ID not found for: $serviceName, trying to fetch from database');
        }
        
        // Try to fetch the service ID from the database
        final serviceResponse = await supabase
            .from('services')
            .select('id')
            .eq('name', serviceName)
            .maybeSingle();
            
        if (serviceResponse != null) {
          final int fetchedServiceId = serviceResponse['id'];
          serviceIdMap[serviceName] = fetchedServiceId;
          
          if (kDebugMode) {
            print('Fetched service ID from database: $fetchedServiceId');
          }
        } else {
          if (kDebugMode) {
            print('Service not found in database: $serviceName');
          }
          return;
        }
      }

      // Fetch subcategories from database
      final subcategoriesResponse = await supabase
          .from('sub_categories')
          .select('id, name')
          .eq('service_id', serviceIdMap[serviceName] ?? 0)
          .order('name');

      if (subcategoriesResponse.isNotEmpty) {
        final List<dynamic> subcategories = List<dynamic>.from(
          subcategoriesResponse,
        );
        final List<String> subcategoryNames = [];
        
        // Create a local map for subcategory IDs
        final Map<String, int> localSubcategoryIdMap = {};

        // Store subcategory names and their IDs
        for (final subcategory in subcategories) {
          final String subcategoryName = subcategory['name'];
          final int subcategoryId = subcategory['id'];

          subcategoryNames.add(subcategoryName);
          localSubcategoryIdMap[subcategoryName] = subcategoryId;
        }

        // Update categories map
        categoriesMap[serviceName] = subcategoryNames;

        if (kDebugMode) {
          print('Loaded subcategories for $serviceName: $subcategoryNames');
          print('Subcategory ID map: $localSubcategoryIdMap');
        }

        // Update state with the subcategories and their IDs
        state = state.copyWith(
          relatedSubCategories: subcategoryNames,
          selectedSubCategories:
              subcategoryNames.isNotEmpty ? [subcategoryNames.first] : [],
          subcategoryIdMap: localSubcategoryIdMap,
        );
        
        if (kDebugMode) {
          print('Updated state with subcategories: ${state.relatedSubCategories}');
          print('Selected first subcategory: ${state.selectedSubCategories}');
          print('Subcategory ID map in state: ${state.subcategoryIdMap}');
        }
      } else {
        if (kDebugMode) {
          print('No subcategories found for service: $serviceName');
        }
        
        // Clear subcategories if none found
        state = state.copyWith(
          relatedSubCategories: [],
          selectedSubCategories: [],
          subcategoryIdMap: {},
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading subcategories for service: $e');
      }
      
      // Clear subcategories on error
      state = state.copyWith(
        relatedSubCategories: [],
        selectedSubCategories: [],
        subcategoryIdMap: {},
      );
    }
  }

  // Modify the selectCategoryFromServiceId method to be simpler
  Future<void> updateCategoryFromServiceId() async {
    try {
      final int serviceId = getServiceId();

      if (serviceId <= 0) {
        if (kDebugMode) {
          print('No service ID selected, skipping category selection');
        }
        return;
      }

      if (kDebugMode) {
        print('Updating category for service ID: $serviceId');
      }

      // Find the main category name that corresponds to the service ID
      String? mainCategoryName;
      for (final entry in serviceIdMap.entries) {
        if (entry.value == serviceId) {
          mainCategoryName = entry.key;
          break;
        }
      }

      if (mainCategoryName == null) {
        if (kDebugMode) {
          print(
            'No main category found for service ID: $serviceId, trying to load from database',
          );
        }

        // Try to fetch the service name from the database
        final serviceResponse =
            await supabase
                .from('services')
                .select('name')
                .eq('id', serviceId)
                .maybeSingle();

        if (serviceResponse != null) {
          mainCategoryName = serviceResponse['name'];
          if (mainCategoryName != null) {
            serviceIdMap[mainCategoryName] = serviceId;
          }

          if (kDebugMode) {
            print('Found service name from database: $mainCategoryName');
          }
        } else {
          // If still not found, try to load from JSON
          await _loadCategoryFromJson(serviceId);
          return;
        }
      }

      // Update the state with the selected main category
      state = state.copyWith(
        selectedMainCategory: mainCategoryName ?? '',
        selectedMainCategoryId: serviceId,
      );

      // Load subcategories for this service and prepopulate UI
      await loadSubcategoriesForService(mainCategoryName ?? '');

      if (kDebugMode) {
        print('Updated category and subcategories for service ID: $serviceId');
        print('Main category: $mainCategoryName');
        print('Subcategories: ${state.relatedSubCategories}');
        print('Selected subcategories: ${state.selectedSubCategories}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating category from service ID: $e');
      }
    }
  }

  Future<void> _saveCategoriesInStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Store the categories in a format that can be used later
      final List<Map<String, dynamic>> formattedCategories = [];

      categoriesMap.forEach((serviceName, subcategories) {
        formattedCategories.add({
          'name': serviceName,
          'id': serviceIdMap[serviceName],
          'subcategories': subcategories,
        });
      });

      // Store the formatted categories and ID mappings in shared preferences
      await prefs.setString(
        'categories',
        jsonEncode({
          'categories': formattedCategories,
          'serviceIdMap': serviceIdMap,
          'subcategoryIdMap': subcategoryIdMap,
        }),
      );

      if (kDebugMode) {
        print('Categories and ID mappings saved to shared preferences');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error saving categories to storage: $e');
      }
    }
  }
}

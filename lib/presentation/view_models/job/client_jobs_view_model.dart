import 'dart:async';

import 'package:build_mate/data/dto/bid_rejection_reponse.dart';
import 'package:build_mate/data/dto/get_bids_response.dart';
import 'package:build_mate/data/dto/job_details_response.dart';
import 'package:build_mate/data/models/job_model.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'package:build_mate/presentation/state/client_jobs_flow_state.dart';

// Provider for the Jobs Tab View Model
final clientJobsViewModelProvider =
    StateNotifierProvider<ClientJobsViewModel, ClientJobsFlowState>((ref) {
      return ClientJobsViewModel();
    });

class ClientJobsViewModel extends StateNotifier<ClientJobsFlowState> {
  final supabase = Supabase.instance.client;
  List<JobModel> jobsList = [];
  RealtimeChannel? _realtimeChannel;
  RealtimeChannel? _bidsRealtimeChannel;

  ClientJobsViewModel() : super(ClientJobsFlowState()) {
    getClientId().then((_) {
      updateOneSignalId();
      _listenToChangesInJobsTable();
      _listenToChangesInBidsTable();
      // Fetch job counts when initialized
    });
  }

  Future<void> updateOneSignalId() async {
    try {
      final client = Supabase.instance.client;
      final currentUserUUID = client.auth.currentUser?.id;

      final response =
          await client
              .from('clients')
              .select('id')
              .eq('supabase_id', currentUserUUID ?? '')
              .single();

      state = state.copyWith(clientId: response['id']);
      // Get the OneSignal User ID (this is the subscription ID)
      final String? onesignalId = await OneSignal.User.getOnesignalId();

      final deviceStateSubscriptionId =
          OneSignal.User.pushSubscription.id ?? '';

      if (kDebugMode) {
        print('CLIENT_ONESIGNAL_ID_RETRIEVED_ON_LOAD: $onesignalId');
      }

      // Only update if we have a valid ID
      if (onesignalId != null && onesignalId.isNotEmpty) {
        await supabase
            .from('clients')
            .update({'one_signal_id': deviceStateSubscriptionId})
            .eq('id', state.clientId);

        if (kDebugMode) {
          print(
            'OneSignal ID updated successfully for client: ${state.clientId}',
          );
        }
      } else {
        // Handle the case where OneSignal ID is not available yet
        if (kDebugMode) {
          print('OneSignal ID not available yet, will retry later');
        }

        // Set up a listener for when the subscription changes
        OneSignal.User.pushSubscription.addObserver((state) async {
          final deviceStateSubscriptionId =
              OneSignal.User.pushSubscription.id ?? '';
          final newId = await OneSignal.User.getOnesignalId();
          if (kDebugMode) {
            print('OneSignal subscription changed, new ID: $newId');
          }

          if (newId != null && newId.isNotEmpty) {
            await supabase
                .from('clients')
                .update({'one_signal_id': deviceStateSubscriptionId})
                .eq('id', this.state.clientId);

            if (kDebugMode) {
              print(
                'OneSignal ID updated on subscription change for client: ${this.state.clientId}',
              );
            }
          }
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating OneSignal ID: $e');
      }
    }
  }

  Future<void> getClientId() async {
    try {
      if (kDebugMode) {
        print('GET_CLIENT_INITIALIZED');
      }
      final client = Supabase.instance.client;
      final currentUserUUID = client.auth.currentUser?.id;

      final response =
          await client
              .from('clients')
              .select('id')
              .eq('supabase_id', currentUserUUID ?? '')
              .single();

      state = state.copyWith(clientId: response['id']);

      // await updateOneSignalId(response['id']);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting client ID: $e');
      }
    }
    final client = Supabase.instance.client;

    final currentUserUUID = client.auth.currentUser?.id;

    final response =
        await client
            .from('clients')
            .select('id')
            .eq('supabase_id', currentUserUUID ?? '')
            .single();

    if (kDebugMode) {
      print('CLIENT_ID_RESPONSE: $response');
    }
    state = state.copyWith(clientId: response['id']);

    // await updateOneSignalId(response['id']);
  }

  // Change the selected tab
  void setSelectedTab(int index) {
    state = state.copyWith(selectedTabIndex: index);
    fetchJobsPostedByClient(); // Reload jobs when tab changes
  }

  void setSelectedJob(JobDetailsResponse job) {
    state = state.copyWith(selectedJob: job);
  }

  Future<void> fetchJobsPostedByClient({bool forceRefresh = false}) async {
    // Set loading state to true
    state = state.copyWith(isLoading: true);

    final client = Supabase.instance.client;

    try {
      final currentUserUUID = client.auth.currentUser?.id;

      final clientIdResponse =
          await client
              .from('clients')
              .select('id')
              .eq('supabase_id', currentUserUUID ?? '')
              .single();

      // Determine which status to filter by based on the selected tab
      String statusFilter;
      switch (state.selectedTabIndex) {
        case 0:
          statusFilter = 'open';
          break;
        case 1:
          statusFilter = 'in_progress';
          break;
        case 2:
          statusFilter = 'completed';
          break;
        default:
          statusFilter = 'open';
      }

      final response = await client
          .from('jobs')
          .select('''
          *,
          service:service_id(name),
          client:client_id(id, name, avatar),
          job_tags:job_tags(
            sub_category_id,
            sub_category:sub_category_id(name)
          ),
          job_images:job_images(image_url),
          bids:bids!bids_job_id_fkey(
            id,
            artisan_id,
            amount,
            status,
            created_at,
            is_selected,
            artisan:artisan_id(name, avatar)
          )
        ''')
          .eq('client_id', clientIdResponse['id'])
          .eq('status', statusFilter)
          .order('created_at', ascending: false);

      if (kDebugMode) {
        print('JOBS POSTED BY CLIENT: $response');
      }

      // Process the response to filter out rejected bids before creating JobDetailsResponse objects
      final processedResponse =
          response.map((job) {
            // Filter out rejected bids
            if (job['bids'] != null) {
              job['bids'] =
                  (job['bids'] as List)
                      .where((bid) => bid['status'] != 'rejected')
                      .toList();
            }
            return job;
          }).toList();

      // Pass the processed response to JobDetailsResponse.fromJson
      final jobs =
          processedResponse
              .map((item) => JobDetailsResponse.fromJson(item))
              .toList();

      // Update state with jobs and set loading to false
      state = state.copyWith(jobs: jobs, isLoading: false);
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching jobs: $e');
      }
      // Update state with error and set loading to false
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  // Listen to changes in jobs table
  void _listenToChangesInJobsTable() {
    // Fetch jobs initially when setting up the listener
    fetchJobsPostedByClient();
    fetchJobCounts();

    _realtimeChannel =
        supabase
            .channel('jobs')
            .onPostgresChanges(
              event: PostgresChangeEvent.all,
              schema: 'public',
              table: 'jobs',
              filter: PostgresChangeFilter(
                type: PostgresChangeFilterType.eq,
                column: 'client_id',
                value: state.clientId,
              ),
              callback: (payload) async {
                if (kDebugMode) {
                  print('Job change detected: ${payload.eventType}');
                  print('Payload: ${payload.toString()}');
                }

                // Handle different event types
                if (payload.eventType == PostgresChangeEvent.insert) {
                  // For new jobs, fetch just that job and add it to the list
                  await _handleJobInserted(payload.newRecord);
                } else if (payload.eventType == PostgresChangeEvent.update) {
                  // For updated jobs, update just that job in the list
                  await _handleJobUpdated(payload.oldRecord, payload.newRecord);
                } else if (payload.eventType == PostgresChangeEvent.delete) {
                  // For deleted jobs, remove just that job from the list
                  _handleJobDeleted(payload.oldRecord);
                }

                // Always update job counts
                await fetchJobCounts();
              },
            )
            .subscribe();
  }

  // Handle a new job being inserted
  Future<void> _handleJobInserted(Map<String, dynamic> newRecord) async {
    // Only process if the job belongs to the current tab
    String statusFilter;
    switch (state.selectedTabIndex) {
      case 0:
        statusFilter = 'open';
        break;
      case 1:
        statusFilter = 'in_progress';
        break;
      case 2:
        statusFilter = 'completed';
        break;
      default:
        statusFilter = 'open';
    }

    if (newRecord['status'] != statusFilter) {
      return;
    }

    try {
      // Fetch the complete job data with all relations
      final response =
          await supabase
              .from('jobs')
              .select('''
            *,
            service:service_id(name),
            client:client_id(id, name, avatar),
            job_tags:job_tags(
              sub_category_id,
              sub_category:sub_category_id(name)
            ),
            job_images:job_images(image_url),
            bids:bids!bids_job_id_fkey(
              id,
              artisan_id,
              amount,
              status,
              created_at,
              is_selected,
              artisan:artisan_id(name, avatar)
            )
          ''')
              .eq('id', newRecord['id'])
              .single();

      // Process the response to filter out rejected bids
      if (response['bids'] != null) {
        response['bids'] =
            (response['bids'] as List)
                .where((bid) => bid['status'] != 'rejected')
                .toList();
      }

      // Create a new JobDetailsResponse
      final newJob = JobDetailsResponse.fromJson(response);

      // Add to the current list
      final updatedJobs = [...state.jobs, newJob];
      // Sort by created_at descending
      updatedJobs.sort((a, b) => b.createdAt!.compareTo(a.createdAt!));

      // Update state
      state = state.copyWith(jobs: updatedJobs);
    } catch (e) {
      if (kDebugMode) {
        print('Error handling job insert: $e');
      }
    }
  }

  // Handle a job being updated
  Future<void> _handleJobUpdated(
    Map<String, dynamic> oldRecord,
    Map<String, dynamic> newRecord,
  ) async {
    final int jobId = newRecord['id'];

    // Check if this job exists in our current list
    final existingJobIndex = state.jobs.indexWhere((job) => job.id == jobId);
    final bool jobExistsInList = existingJobIndex != -1;

    // Get the current tab's status filter
    String statusFilter;
    switch (state.selectedTabIndex) {
      case 0:
        statusFilter = 'open';
        break;
      case 1:
        statusFilter = 'in_progress';
        break;
      case 2:
        statusFilter = 'completed';
        break;
      default:
        statusFilter = 'open';
    }

    // Case 1: Status changed to something not matching current tab - remove from list
    if (newRecord['status'] != statusFilter && jobExistsInList) {
      final updatedJobs = [...state.jobs];
      updatedJobs.removeAt(existingJobIndex);
      state = state.copyWith(jobs: updatedJobs);
      return;
    }

    // Case 2: Status changed to match current tab - add to list if not already there
    if (newRecord['status'] == statusFilter) {
      try {
        // Fetch the complete job data with all relations
        final response =
            await supabase
                .from('jobs')
                .select('''
              *,
              service:service_id(name),
              client:client_id(id, name, avatar),
              job_tags:job_tags(
                sub_category_id,
                sub_category:sub_category_id(name)
              ),
              job_images:job_images(image_url),
              bids:bids!bids_job_id_fkey(
                id,
                artisan_id,
                amount,
                status,
                created_at,
                is_selected,
                artisan:artisan_id(name, avatar)
              )
            ''')
                .eq('id', jobId)
                .single();

        // Process the response to filter out rejected bids
        if (response['bids'] != null) {
          response['bids'] =
              (response['bids'] as List)
                  .where((bid) => bid['status'] != 'rejected')
                  .toList();
        }

        // Create a JobDetailsResponse
        final updatedJob = JobDetailsResponse.fromJson(response);

        if (jobExistsInList) {
          // Update existing job in the list
          final updatedJobs = [...state.jobs];
          updatedJobs[existingJobIndex] = updatedJob;
          state = state.copyWith(jobs: updatedJobs);
        } else {
          // Add new job to the list
          final updatedJobs = [...state.jobs, updatedJob];
          // Sort by created_at descending
          updatedJobs.sort((a, b) => b.createdAt!.compareTo(a.createdAt!));
          state = state.copyWith(jobs: updatedJobs);
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error handling job update: $e');
        }
      }
    }
  }

  // Handle a job being deleted
  void _handleJobDeleted(Map<String, dynamic> oldRecord) {
    // Remove the job from the list if it exists
    final updatedJobs =
        state.jobs.where((job) => job.id != oldRecord['id']).toList();
    state = state.copyWith(jobs: updatedJobs);
  }

  // Listen to changes in bids table
  void _listenToChangesInBidsTable() {
    _bidsRealtimeChannel =
        supabase
            .channel('bids')
            .onPostgresChanges(
              event: PostgresChangeEvent.all,
              schema: 'public',
              table: 'bids',
              callback: (payload) async {
                if (kDebugMode) {
                  print('Bid change detected: ${payload.eventType}');
                  print('Payload: ${payload.toString()}');
                }

                // When a bid is added, updated, or deleted, update the affected job
                if (payload.eventType == PostgresChangeEvent.insert ||
                    payload.eventType == PostgresChangeEvent.update ||
                    payload.eventType == PostgresChangeEvent.delete) {
                  int? jobId;

                  // For insert and update events, check new record
                  if (payload.newRecord.isNotEmpty &&
                      payload.newRecord.containsKey('job_id')) {
                    jobId = payload.newRecord['job_id'];
                  }
                  // For delete events, check old record
                  else if (payload.oldRecord.isNotEmpty &&
                      payload.oldRecord.containsKey('job_id')) {
                    jobId = payload.oldRecord['job_id'];
                  }

                  if (kDebugMode) {
                    print('Job ID from bid change: $jobId');
                  }

                  if (jobId != null) {
                    // Find the job in the current list
                    final jobIndex = state.jobs.indexWhere(
                      (job) => job.id == jobId,
                    );

                    if (jobIndex != -1) {
                      // Update just this job
                      await _updateSingleJob(jobId, jobIndex);
                    }

                    // If we're currently viewing bids for this job, refresh those too
                    if (state.selectedJob != null &&
                        state.selectedJob!.id == jobId) {
                      await fetchBidsForJob(forceRefresh: true);
                    }
                  }
                }
              },
            )
            .subscribe();
  }

  // Helper method to update a single job in the list
  Future<void> _updateSingleJob(int jobId, int jobIndex) async {
    try {
      // Fetch the updated job data
      final response =
          await supabase
              .from('jobs')
              .select('''
            *,
            service:service_id(name),
            client:client_id(id, name, avatar),
            job_tags:job_tags(
              sub_category_id,
              sub_category:sub_category_id(name)
            ),
            job_images:job_images(image_url),
            bids:bids!bids_job_id_fkey(
              id,
              artisan_id,
              amount,
              status,
              created_at,
              is_selected,
              artisan:artisan_id(name, avatar)
            )
          ''')
              .eq('id', jobId)
              .single();

      // Process the response to filter out rejected bids
      if (response['bids'] != null) {
        response['bids'] =
            (response['bids'] as List)
                .where((bid) => bid['status'] != 'rejected')
                .toList();
      }

      // Create a JobDetailsResponse
      final updatedJob = JobDetailsResponse.fromJson(response);

      // Update the job in the list
      final updatedJobs = [...state.jobs];
      updatedJobs[jobIndex] = updatedJob;
      state = state.copyWith(jobs: updatedJobs);
    } catch (e) {
      if (kDebugMode) {
        print('Error updating single job: $e');
      }
    }
  }

  @override
  void dispose() {
    _realtimeChannel?.unsubscribe();
    _bidsRealtimeChannel?.unsubscribe();
    super.dispose();
  }

  Future<BidRejectionResponse> rejectBid({required int bidId}) async {
    final client = Supabase.instance.client;

    if (kDebugMode) {
      print('REJECT_BID_ID: $bidId');
    }

    try {
      // First, get the job_id for this bid to refresh the UI later
      final bidData =
          await client.from('bids').select('job_id').eq('id', bidId).single();

      final int jobId = bidData['job_id'];

      final response = await client
          .rpc('reject_bid_artisan', params: {'bid_id': bidId})
          .select()
          .single()
          .then((response) async {
            await rejectNotification(response['artisan_id'] as int);
            if (kDebugMode) {
              print('REJECTED_RESPONSE: ${response.toString()}');
            }

            // Update local state immediately to reflect the rejected bid
            if (state.selectedJob != null && state.selectedJob!.id == jobId) {
              // Update the status of the rejected bid in the local state
              final updatedBids =
                  state.bids.map((bid) {
                    if (bid.id == bidId) {
                      return bid.copyWith(status: 'rejected');
                    }
                    return bid;
                  }).toList();

              state = state.copyWith(bids: updatedBids);

              // Also fetch fresh data from the server
              await fetchBidsForJob();
            }

            return BidRejectionResponse(
              success: true,
              bidId: response['bid_id'] as int,
              artisanId: response['artisan_id'] as int,
              newStatus: response['status'] as String,
            );
          });

      // Also refresh the jobs list to update bid counts
      await fetchJobsPostedByClient();

      return response;
    } catch (e) {
      if (kDebugMode) {
        print('REJECT_ERROR_MESSAGE: ${e.toString()}');
      }
      return BidRejectionResponse(
        success: false,
        bidId: bidId,
        newStatus: '',
        errorMessage: e.toString(),
      );
    }
  }

  Future<void> sendAcceptNotification(int artisanId) async {
    final res = await supabase.functions.invoke(
      'send-bid-acceptance-notification',
      body: {'artisanId': artisanId},
    );
    final data = res.data;
    if (kDebugMode) {
      print('Notification sent: ${data.toString()}');
    }
  }

  Future<void> rejectNotification(int artisanId) async {
    final res = await supabase.functions.invoke(
      'send-bid-acceptance-notification',
      body: {'artisanId': artisanId},
    );
    final data = res.data;
    if (kDebugMode) {
      print('Notification sent: ${data.toString()}');
    }
  }

  Future<Map<String, dynamic>> acceptBid({
    required int jobId,
    required int bidId,
    required int artisanId,
  }) async {
    final client = Supabase.instance.client;

    try {
      final response = await client
          .rpc(
            'accept_bid_transaction',
            params: {
              'p_job_id': jobId,
              'p_bid_id': bidId,
              'p_artisan_id': artisanId,
            },
          )
          .select()
          .single()
          .then((value) async {
            await sendAcceptNotification(artisanId);
            if (kDebugMode) {
              print('ACCEPTED_BID_RESPONSE: ${value.toString()}');
            }

            // Manually refresh the jobs list to update status and bid counts
            await fetchJobsPostedByClient();

            return {
              'success': true,
              'job_id': value['job_id'],
              'new_status': value['new_status'],
              'artisan_id': value['artisan_id'],
            };
          });

      return response;
    } catch (e) {
      if (kDebugMode) {
        print('ACCEPT_BID_ERROR: ${e.toString()}');
      }
      return {'success': false, 'error': e.toString()};
    }
  }

  void selectedJob(JobDetailsResponse job) {
    state = state.copyWith(selectedJob: job);
  }

  Future<void> fetchBidsForJob({bool forceRefresh = false}) async {
    final client = Supabase.instance.client;

    try {
      // Clear the bids first if forcing refresh
      if (forceRefresh) {
        state = state.copyWith(bids: []);
      }

      final response = await client
          .from('bids')
          .select('''
          *,
          artisan:artisan_id(
            id,
            name,
            avatar,
            about
          ),
          job:job_id(
            id,
            title:job_description,
            budget
          )
        ''')
          .eq('job_id', state.selectedJob?.id ?? 0)
          .neq('status', 'rejected') // Filter out rejected bids
          .order('created_at', ascending: false);

      if (kDebugMode) {
        print('BIDS: $response');
        print('BIDS COUNT: ${response.length}');
      }

      // Map the response to GetBidResponse objects
      final bids = response.map((bid) => GetBidResponse.fromJson(bid)).toList();

      // Update the state with the new bids
      state = state.copyWith(bids: bids);
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching bids: $e');
      }
      // Keep existing bids in case of error
    }
  }

  // Add a method to fetch job counts for all tabs
  Future<void> fetchJobCounts() async {
    final client = Supabase.instance.client;

    try {
      final currentUserUUID = client.auth.currentUser?.id;

      final clientIdResponse =
          await client
              .from('clients')
              .select('id')
              .eq('supabase_id', currentUserUUID ?? '')
              .single();

      // Get counts for each status
      final openJobsResponse = await client
          .from('jobs')
          .select('id')
          .eq('client_id', clientIdResponse['id'])
          .eq('status', 'open');

      final inProgressJobsResponse = await client
          .from('jobs')
          .select('id')
          .eq('client_id', clientIdResponse['id'])
          .eq('status', 'in_progress');

      final completedJobsResponse = await client
          .from('jobs')
          .select('id')
          .eq('client_id', clientIdResponse['id'])
          .eq('status', 'completed');

      // Update state with job counts
      state = state.copyWith(
        openJobsCount: openJobsResponse.length,
        inProgressJobsCount: inProgressJobsResponse.length,
        completedJobsCount: completedJobsResponse.length,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching job counts: $e');
      }
    }
  }

  Future<Map<String, dynamic>> markJobAsComplete(int jobId) async {
    try {
      if (kDebugMode) {
        print('Marking job $jobId as complete');
      }

      // Update the job status to 'completed' in the database
      final response =
          await supabase
              .from('jobs')
              .update({'status': 'completed'})
              .eq('id', jobId)
              .select()
              .single();

      if (kDebugMode) {
        print('Job marked as complete: $response');
      }

      // Send notification to the artisan
      try {
        // Get the selected artisan for this job
        final bidResponse =
            await supabase
                .from('bids')
                .select('artisan_id')
                .eq('job_id', jobId)
                .eq('is_selected', true)
                .single();

        final artisanId = bidResponse['artisan_id'];

        // Send notification
        final res = await supabase.functions.invoke(
          'send-job-completion-notification',
          body: {'artisanId': artisanId, 'jobId': jobId},
        );

        final data = res.data;
        if (kDebugMode) {
          print('Completion notification sent: ${data.toString()}');
        }
      } catch (notificationError) {
        if (kDebugMode) {
          print('Error sending completion notification: $notificationError');
        }
        // Continue execution even if notification fails
      }

      // The job will be automatically moved to the completed tab via realtime updates

      return {'success': true, 'job': response};
    } catch (e) {
      if (kDebugMode) {
        print('Error marking job as complete: $e');
      }
      return {'success': false, 'error': e.toString()};
    }
  }
}

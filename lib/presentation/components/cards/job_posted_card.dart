import 'package:build_mate/presentation/components/helper_widgets/spacing_widgets.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/theme/font/typography.dart';

class JobPostedCard extends StatelessWidget {
  final String title;
  final String badgeText;
  final List<String> categories;
  final String description;
  final String datePosted;
  final String status;
  final int jobNumber;
  final int bidCount;
  final String serviceDate; // Add service date parameter
  final VoidCallback onTap;
  final List<String>? bidderAvatars; // Add list of bidder avatars
  final VoidCallback? onMarkAsCompletePressed;
  final bool isMarkingComplete;

  const JobPostedCard({
    super.key,
    required this.title,
    required this.badgeText,
    required this.categories,
    required this.description,
    required this.datePosted, // This can now be a DateTime or a String
    required this.status,
    required this.jobNumber,
    this.bidCount = 0,
    required this.serviceDate, // Make service date required
    required this.onTap,
    this.bidderAvatars = const [], // Initialize bidder avatars as empty list
    this.onMarkAsCompletePressed,
    this.isMarkingComplete = false,
  });

  // Modified method to handle already formatted strings
  String _formatTimeAgo(dynamic dateTime) {
    // If it's already a formatted string like "1 day ago", return it directly
    if (dateTime is String &&
        (dateTime.contains('ago') || dateTime == 'Just now')) {
      return dateTime;
    }

    // Otherwise parse the date
    final DateTime parsedDateTime =
        dateTime is String ? DateTime.parse(dateTime) : dateTime;

    final now = DateTime.now();
    final difference = now.difference(parsedDateTime);

    if (difference.inDays > 7) {
      return '${parsedDateTime.day}/${parsedDateTime.month}/${parsedDateTime.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
    } else {
      return 'Just now';
    }
  }

  // Format service date to "8 May, 2025" format
  String _formatServiceDate(String dateString) {
    try {
      // Try to parse the date string
      final DateTime date = DateTime.parse(dateString);

      // List of month names
      final List<String> months = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
      ];

      // Format as "8 May, 2025"
      return '${date.day} ${months[date.month - 1]}, ${date.year}';
    } catch (e) {
      // If parsing fails, return the original string
      return dateString;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Create a list of tag colors to cycle through
    final List<Color> tagColors = [
      const Color(0xFFE3F2FD), // Light blue
      const Color(0xFFE8F5E9), // Light green
      const Color(0xFFFFF3E0), // Light orange
    ];

    final List<Color> textColors = [
      const Color(0xFF1565C0), // Dark blue
      const Color(0xFF2E7D32), // Dark green
      const Color(0xFFE65100), // Dark orange
    ];

    // Get appropriate status display text and color
    String displayStatus = status;
    Color displayColor = const Color(0xFFE3F2FD);

    // Format status text for display (capitalize first letter of each word)
    if (status == 'open') {
      displayStatus = 'Open';
      displayColor = Colors.blue;
    } else if (status == 'in_progress') {
      displayStatus = 'In Progress';
      displayColor = Colors.orange;
    } else if (status == 'completed') {
      displayStatus = 'Completed';
      displayColor = Colors.green;
    }

    // Build status widget or action button based on status
    Widget buildStatusOrAction() {
      if (status.toLowerCase() == 'in_progress' && onMarkAsCompletePressed != null) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.green[600],
            borderRadius: BorderRadius.circular(8),
          ),
          child: InkWell(
            onTap: isMarkingComplete ? null : onMarkAsCompletePressed,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                isMarkingComplete 
                  ? SizedBox(
                      width: 12,
                      height: 12,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Icon(Icons.check_circle_outline, size: 12, color: Colors.white),
                const SizedBox(width: 4),
                Text(
                  isMarkingComplete ? 'Completing...' : 'Mark as complete',
                  style: MyTypography.Medium.copyWith(
                    fontSize: 11,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        );
      } else {
        return Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 10,
            vertical: 5,
          ),
          decoration: BoxDecoration(
            color: displayColor.withAlpha((0.1 * 255).round()),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: displayColor.withAlpha((0.3 * 255).round()),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                status == 'open'
                    ? Icons.fiber_new_outlined
                    : status == 'in_progress'
                    ? Icons.pending_outlined
                    : status == 'completed'
                    ? Icons.check_circle_outline
                    : Icons.info_outline,
                size: 14,
                color: displayColor,
              ),
              const SizedBox(width: 4),
              Text(
                displayStatus,
                style: MyTypography.Medium.copyWith(
                  fontSize: 12,
                  color: displayColor,
                ),
              ),
            ],
          ),
        );
      }
    }

    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        height: getScreenHeight(context) * 0.3,
        child: Card(
          margin: EdgeInsets.zero,
          elevation: 2,
          color: Colors.white, // Explicitly set card background to white
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title only (status moved to bottom)
                Text(
                  title,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: MyTypography.SemiBold.copyWith(fontSize: 16),
                ),
                const SizedBox(height: 12),

                // Description
                Text(
                  description,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: MyTypography.Regular.copyWith(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 12),

                // Enhanced Categories with colorful tags - made smaller
                SizedBox(
                  height: 26, // Reduced height from 32 to 26
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: categories.length,
                    itemBuilder: (context, index) {
                      // Use modulo to cycle through colors if there are more than 3 tags
                      final colorIndex = index % tagColors.length;

                      return Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 4,
                          ), // Reduced padding
                          decoration: BoxDecoration(
                            color: tagColors[colorIndex],
                            borderRadius: BorderRadius.circular(
                              12,
                            ), // Reduced border radius
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withAlpha(
                                  (0.05 * 255).round(),
                                ),
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                          child: Text(
                            categories[index],
                            style: MyTypography.Medium.copyWith(
                              fontSize: 11, // Reduced font size
                              color: textColors[colorIndex],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),

                // Service date below tags
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(
                      Icons.event_available,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'Start by: ${_formatServiceDate(serviceDate)}',
                      style: MyTypography.SemiBold.copyWith(
                        fontSize: 13,
                        color: Colors.grey[700],
                      ),
                    ),
                  ],
                ),

                // Spacer to push date and bids to bottom
                const Spacer(),

                // Date row
                Row(
                  children: [
                    Icon(
                      Icons.calendar_today_outlined,
                      size: 14,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      datePosted is DateTime
                          ? _formatTimeAgo(datePosted)
                          : _formatTimeAgo(datePosted),
                      style: MyTypography.Regular.copyWith(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),

                // Bids and status row - now below the date
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Bids section (left)
                    Row(
                      children: [
                        // Overlapping bidder avatars
                        if (bidderAvatars != null && bidderAvatars!.isNotEmpty)
                          SizedBox(
                            width:
                                bidderAvatars!.length > 1
                                    ? 60
                                    : 24, // Width depends on number of avatars
                            height: 24,
                            child: Stack(
                              children: [
                                for (
                                  int i = 0;
                                  i < bidderAvatars!.length && i < 3;
                                  i++
                                )
                                  Positioned(
                                    left: i * 16.0, // Overlap each avatar
                                    child: Container(
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: Colors.white,
                                          width: 1.5,
                                        ),
                                      ),
                                      child: CircleAvatar(
                                        radius: 12,
                                        backgroundImage:
                                            bidderAvatars![i].startsWith('http')
                                                ? NetworkImage(bidderAvatars![i])
                                                    as ImageProvider
                                                : AssetImage(bidderAvatars![i]),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.blue[700],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.people_outline,
                                size: 14,
                                color: Colors.white,
                              ),
                              const SizedBox(width: 4),
                              status == 'open'
                                  ? Text(
                                    '$bidCount ${bidCount == 1 ? 'bid' : 'bids'}',
                                    style: MyTypography.Medium.copyWith(
                                      fontSize: 12,
                                      color: Colors.white,
                                    ),
                                  )
                                  : 
                                  Text(''),
                            ],
                          ),
                        ),
                      ],
                    ),

                    // Status or action button (right)
                    buildStatusOrAction(),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

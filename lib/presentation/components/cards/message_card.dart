import 'package:build_mate/theme/colors.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/theme/font/typography.dart';

class MessageCard extends StatelessWidget {
  final String senderName;
  final String message;
  final String time;
  final String avatarUrl;
  final bool isUnread;
  final VoidCallback onTap;

  const MessageCard({
    super.key,
    required this.senderName,
    required this.message,
    required this.time,
    required this.avatarUrl,
    this.isUnread = false,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      print('Building MessageCard for $senderName');
      print('Avatar URL: $avatarUrl');
      print('Is network image: ${avatarUrl.startsWith('http')}');
    }
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha((0.05 * 255).round()),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Avatar
            CircleAvatar(
              radius: 24,
              backgroundImage: avatarUrl.startsWith('http') 
                  ? NetworkImage(avatarUrl) as ImageProvider
                  : AssetImage(avatarUrl),
              backgroundColor: Colors.grey[200],
            ),
            const SizedBox(width: 12),
            // Message Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    senderName,
                    style: MyTypography.SemiBold.copyWith(
                      fontSize: 16,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    message,
                    style: MyTypography.Regular.copyWith(
                      fontSize: 14,
                      color: Colors.grey[600],
                      overflow: TextOverflow.ellipsis,
                    ),
                    maxLines: 1,
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            // Time
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  time,
                  style: MyTypography.Regular.copyWith(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                if (isUnread)
                  Container(
                    margin: const EdgeInsets.only(top: 4),
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: darkBlueColor,
                      shape: BoxShape.circle,
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
